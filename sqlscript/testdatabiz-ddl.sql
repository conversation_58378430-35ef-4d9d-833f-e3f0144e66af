ALTER TABLE `tb_test_data_info`
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT ''Trims系统实验室标识'' AFTER `rd_report_id`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT ''订单号'' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT ''报告号'' AFTER `order_no`,
    ADD COLUMN `test_result_full_name` varchar(500) NULL COMMENT ''测试项名称，拼接字段'' AFTER `report_no`,
    ADD COLUMN `test_result_seq` int(11) NULL COMMENT ''测试项顺序'' AFTER `test_result_full_name`,
    ADD COLUMN `result_value` varchar(150) NULL COMMENT ''测试结果'' AFTER `test_result_seq`,
    ADD COLUMN `result_value_remark` varchar(500) NULL COMMENT ''测试结果备注'' AFTER `result_value`,
    ADD COLUMN `result_unit` varchar(1024) NULL COMMENT ''测试结果单位'' AFTER `result_value_remark`,
    ADD COLUMN `fail_flag` tinyint(1) NULL COMMENT ''失败标识'' AFTER `result_unit`,
    ADD COLUMN `limit_value_full_name` varchar(500) NULL COMMENT ''参考标准，拼接字段'' AFTER `fail_flag`;



ALTER TABLE `tb_test_data_matrix_info`
    ADD COLUMN `lab_id` bigint(20) NULL COMMENT ''Trims系统实验室标识'' AFTER `ModifiedDate`,
    ADD COLUMN `order_no` varchar(50) NULL COMMENT ''订单号'' AFTER `lab_id`,
    ADD COLUMN `report_no` varchar(50) NULL COMMENT ''报告号'' AFTER `order_no`,
    ADD COLUMN `test_matrix_group_id` int(11) NULL COMMENT ''测试单位分组标识'' AFTER `report_no`,
    ADD COLUMN `test_line_instance_id` varchar(64) NULL COMMENT ''测试项实例标识'' AFTER `test_matrix_group_id`,
    ADD COLUMN `evaluation_name` varchar(255) NULL COMMENT ''测试项名称'' AFTER `test_line_instance_id`,
    ADD COLUMN `test_line_status` varchar(255) NULL COMMENT ''测试项状态'' AFTER `evaluation_name`,
    ADD COLUMN `test_line_remark` varchar(255) NULL COMMENT ''测试项备注'' AFTER `test_line_status`,
    ADD COLUMN `citation_full_name` varchar(255) NULL COMMENT ''测试标准拼接名称'' AFTER `test_line_remark`,
    ADD COLUMN `sample_instance_id` varchar(36) NULL COMMENT ''样品实例标识'' AFTER `citation_full_name`,
    ADD COLUMN `sample_group` text NULL COMMENT ''样品分组信息'' AFTER `sample_instance_id`,
    ADD COLUMN `sample_parent_id` varchar(36) NULL COMMENT ''样品父级标识'' AFTER `sample_group`,
    ADD COLUMN `sample_type` varchar(255) NULL COMMENT ''样品类型'' AFTER `sample_parent_id`,
    ADD COLUMN `category` varchar(255) NULL COMMENT ''样品分类'' AFTER `sample_type`,
    ADD COLUMN `material_color` varchar(255) NULL COMMENT ''物料颜色'' AFTER `category`,
    ADD COLUMN `composition` varchar(255) NULL COMMENT ''物料材质'' AFTER `material_color`,
    ADD COLUMN `material_description` varchar(255) NULL COMMENT ''物料描述'' AFTER `composition`,
    ADD COLUMN `material_end_use` varchar(255) NULL COMMENT ''物料用途'' AFTER `material_description`,
    ADD COLUMN `applicable_flag` varchar(255) NULL COMMENT ''NC样品标识'' AFTER `material_end_use`,
    ADD COLUMN `material_other_sample_info` varchar(255) NULL COMMENT ''其他样品信息'' AFTER `applicable_flag`,
    ADD COLUMN `material_remark` varchar(255) NULL COMMENT ''样品备注信息'' AFTER `material_other_sample_info`,
    ADD COLUMN `conclusion_code` varchar(255) NULL COMMENT ''测试结论编码'' AFTER `material_remark`,
    ADD COLUMN `customer_conclusion` varchar(50) NULL COMMENT ''客户测试结论'' AFTER `conclusion_code`,
    ADD COLUMN `conclusion_remark` varchar(255) NULL COMMENT ''测试结论备注'' AFTER `customer_conclusion`,
    ADD COLUMN `rd_report_id` bigint(20) NULL AFTER `BizVersionId`,
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL ON
UPDATE CURRENT_TIMESTAMP(0) COMMENT '' DB 自动更新，不允许程序设置 '' AFTER `rd_report_id`;


CREATE TABLE `tb_test_sample`
(
    `id`                      bigint(20) NOT NULL COMMENT '' ID,
    Primary key '',
    `sample_instance_id`      varchar(64)   DEFAULT NULL,
    `sample_parent_id`        varchar(36)   DEFAULT NULL,
    `rd_report_matrix_id`     bigint(20) DEFAULT NULL,
    `sample_no`               varchar(60)   DEFAULT NULL COMMENT '' 样品编号 '',
    `sample_type`             int(11) DEFAULT NULL COMMENT '' 样品类型 '',
    `sample_seq`              int(11) DEFAULT NULL COMMENT '' 样品顺序 '',
    `order_no`                varchar(50)   DEFAULT NULL COMMENT '' 订单编号 '',
    `category`                varchar(10)   DEFAULT NULL COMMENT '' 物料分类 '',
    `description`             varchar(2048) DEFAULT NULL COMMENT '' 物料描述 '',
    `composition`             varchar(500)  DEFAULT NULL COMMENT '' 物料材质 '',
    `color`                   varchar(500)  DEFAULT NULL COMMENT '' 物料颜色 '',
    `sample_remark`           varchar(4000) DEFAULT NULL COMMENT '' 样品备注信息 '',
    `end_use`                 varchar(500)  DEFAULT NULL COMMENT '' 物料用途 '',
    `material`                varchar(300)  DEFAULT NULL COMMENT '' 物料名称 '',
    `other_sample_info`       text COMMENT '' 其它样品信息 '',
    `applicable_flag`         tinyint(1) DEFAULT NULL COMMENT '' NC 样品标识 '',
    `active_indicator`        tinyint(1) NOT NULL DEFAULT '' 1 '' COMMENT '' 0: inactive, 1
    :
    active
    '',
    `created_date`            datetime      DEFAULT NULL COMMENT '' CreatedDate '',
    `created_by`              varchar(50)   DEFAULT NULL COMMENT '' CreatedBy '',
    `modified_date`           datetime      DEFAULT NULL COMMENT '' ModifiedDate '',
    `modified_by`             varchar(50)   DEFAULT NULL COMMENT '' ModifiedBy '',
    `last_modified_timestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3),
    PRIMARY KEY (`id`),
    KEY                       `idx_orderNo_sampleNo` (`order_no`,`sample_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_test_sample_group`
(
    `id`                      bigint(20) NOT NULL COMMENT '' ID,
    Primary key '',
    `sample_group_id`         varchar(64) DEFAULT NULL,
    `sample_id`               varchar(64) DEFAULT NULL COMMENT '' FK tb_TestSample '',
    `main_sample_flag`        int(11) DEFAULT NULL COMMENT '' 主测试样标识 '',
    `active_indicator`        tinyint(1) NOT NULL DEFAULT '' 1 '' COMMENT '' 0: inactive, 1
    :
    active
    '',
    `created_date`            datetime    DEFAULT NULL COMMENT '' CreatedDate '',
    `created_by`              varchar(50) DEFAULT NULL COMMENT '' CreatedBy '',
    `modified_date`           datetime    DEFAULT NULL COMMENT '' ModifiedDate '',
    `modified_by`             varchar(50) DEFAULT NULL COMMENT '' ModifiedBy '',
    `last_modified_timestamp` timestamp(3) NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3),
    PRIMARY KEY (`id`),
    KEY                       `FK_Reference_12` (`sample_group_id`),
    KEY                       `FK_Reference_13` (`sample_id`),
    KEY                       `index_LastModifiedTimestamp` (`last_modified_timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_report_trf_rel`
(
    `id`                      bigint(20) NOT NULL COMMENT '' RD 唯一标识 '',
    `lab_id`                  bigint(20) NOT NULL COMMENT '' Trims 实验室标识 '',
    `bu_id`                   bigint(20) DEFAULT NULL COMMENT '' 产品线线标识 '',
    `trf_ref_system_id`       int(11) unsigned NOT NULL DEFAULT '' 0 '' COMMENT '' 测试申请单对接系统标识 '',
    `trf_no`                  varchar(64) NOT NULL DEFAULT '''' COMMENT '' 测试申请单编码 '',
    `order_system_id`         int(11) unsigned NOT NULL COMMENT '' 订单执行系统标识 '',
    `order_no`                varchar(64) NOT NULL COMMENT '' 订单编码 '',
    `report_no`               varchar(64) NOT NULL COMMENT '' 报告编码 '',
    `active_indicator`        tinyint(1) NOT NULL DEFAULT '' 1 '' COMMENT '' 0: inactive, 1
    :
    active
    '',
    `created_by`              varchar(50)          DEFAULT NULL COMMENT '' 创建人 '',
    `created_date`            datetime    NOT NULL COMMENT '' 创建时间 '',
    `modified_by`             varchar(50)          DEFAULT NULL COMMENT '' 修改人 '',
    `modified_date`           datetime    NOT NULL COMMENT '' 修改时间 '',
    `last_modified_timestamp` timestamp(3) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '' DB 自动更新，不允许程序设置 '',
    PRIMARY KEY (`id`),
    KEY                       `idx_trfSysId_trfNo` (`trf_ref_system_id`,`trf_no`) USING BTREE,
    KEY                       `idx_orderSysId_OrderNo` (`order_system_id`,`order_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=''trf report关系表'';

CREATE TABLE `tb_report`
(
    `id`                      bigint(20) NOT NULL COMMENT '' Report Data 唯一标识 '',
    `lab_id`                  bigint(20) NOT NULL COMMENT '' Trims系统实验室标识 '',
    `bu_id`                   bigint(20) DEFAULT NULL COMMENT '' Trims系统产品线ID '',
    `system_id`               bigint(20) DEFAULT NULL,
    `report_id`               varchar(50)   DEFAULT NULL COMMENT '' 执行系统报告标识 '',
    `report_no`               varchar(50) NOT NULL COMMENT '' 执行系统报告号 '',
    `original_report_no`      varchar(50)   DEFAULT NULL COMMENT '' 执行系统父级报告号 '',
    `report_header`           varchar(300)  DEFAULT NULL COMMENT '' 报告抬头 '',
    `report_address`          varchar(500)  DEFAULT NULL COMMENT '' 报告地址 '',
    `report_status`           int(4) unsigned zerofill DEFAULT NULL COMMENT '' 报告状态 '',
    `report_due_date`         datetime      DEFAULT NULL COMMENT '' 报告到期时间 '',
    `report_certificate_name` varchar(300)  DEFAULT NULL COMMENT '' 报告资质名称 '',
    `report_created_by`       varchar(50)   DEFAULT NULL COMMENT '' 报告创建人 '',
    `report_created_date`     datetime      DEFAULT NULL COMMENT '' 报告创建时间 '',
    `report_approver_by`      varchar(500)  DEFAULT NULL COMMENT '' 报告审批人 '',
    `report_approver_date`    datetime      DEFAULT NULL COMMENT '' 报告审批时间 '',
    `softcopy_delivery_date`  datetime      DEFAULT NULL COMMENT '' 报告发送时间 '',
    `conclusion_code`         varchar(500)  DEFAULT NULL COMMENT '' 报告整体结论 '',
    `customer_conclusion`     varchar(4000) DEFAULT NULL COMMENT '' 报告客户结论 '',
    `review_conclusion`       varchar(500)  DEFAULT NULL COMMENT '' 报告客户审核结论 '',
    `conclusion_remark`       varchar(1000) DEFAULT NULL COMMENT '' 结论备注信息 '',
    `active_indicator`        tinyint(1) DEFAULT '' 1 '' COMMENT '' 0: inactive, 1
    :
    active
    '',
    `created_by`              varchar(50)   DEFAULT NULL COMMENT '' 创建人 '',
    `created_date`            datetime    NOT NULL COMMENT '' 创建时间 '',
    `modified_by`             varchar(50)   DEFAULT NULL COMMENT '' 修改人 '',
    `modified_date`           datetime    NOT NULL COMMENT '' 修改时间 '',
    `last_modified_timestamp` timestamp(3) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '' DB 自动更新，不允许程序设置 '',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                       `idx_labId_reportNo` (`lab_id`,`report_no`) USING BTREE,
    KEY                       `idx_createdDate` (`created_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=''report信息主表'';


CREATE TABLE `tb_report_ext`
(
    `id`           bigint(20) NOT NULL COMMENT '' RD 唯一标识 '',
    `rd_report_id` bigint(20) unsigned NOT NULL COMMENT '' RD Report 唯一标识 '',
    `request_json` mediumtext,
    PRIMARY KEY (`id`),
    KEY            `idx_rdReportId` (`rd_report_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=''report扩展信息表'';

CREATE TABLE `tb_report_invoice`
(
    `id`                 bigint(20) NOT NULL,
    `lab_id`             bigint(20) DEFAULT NULL COMMENT '' Trims系统实验室标识 '',
    `rd_report_id`       bigint(20) DEFAULT NULL,
    `system_id`          bigint(20) DEFAULT NULL,
    `order_no`           varchar(50)    DEFAULT NULL COMMENT '' 订单号 '',
    `report_no`          varchar(50)    DEFAULT NULL COMMENT '' 报告号 '',
    `boss_order_no`      varchar(100)   DEFAULT NULL COMMENT '' Boss订单号 '',
    `product_code`       varchar(500)   DEFAULT NULL COMMENT '' 产品编码 '',
    `cost_center`        varchar(500)   DEFAULT NULL COMMENT '' 费用中心 '',
    `project_template`   varchar(500)   DEFAULT NULL COMMENT '' 项目模板 '',
    `invoice_date`       datetime       DEFAULT NULL COMMENT '' 发票开具时间 '',
    `invoice_no`         varchar(50)    DEFAULT NULL COMMENT '' Boss 发票号 '',
    `currency`           varchar(100)   DEFAULT NULL COMMENT '' 币种 '',
    `total_amount`       decimal(20, 6) DEFAULT NULL COMMENT '' 总金额 '',
    `invoice_status`     int(10) unsigned DEFAULT NULL COMMENT '' 发票状态 '',
    `active_indicator`   tinyint(1) DEFAULT '' 1 '' COMMENT '' 0: inactive, 1
    :
    active
    '',
    `created_by`         varchar(50)    DEFAULT NULL COMMENT '' 创建人 '',
    `created_date`       datetime       DEFAULT NULL COMMENT '' 创建时间 '',
    `modified_by`        varchar(50)    DEFAULT NULL COMMENT '' 修改人 '',
    `modified_date`      datetime       DEFAULT NULL COMMENT '' 修改时间 '',
    `last_modified_date` datetime       DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '' DB 自动更新，不允许程序设置 '',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_report_lang`
(
    `id`             bigint(20) NOT NULL COMMENT '' RD 唯一标识 '',
    `rd_report_id`   bigint(20) NOT NULL COMMENT '' RD Report 标识 '',
    `language_id`    tinyint(4) DEFAULT NULL COMMENT '' 语言ID '',
    `report_header`  varchar(300) DEFAULT NULL COMMENT '' 报告抬头 '',
    `report_address` varchar(500) DEFAULT NULL COMMENT '' 报告地址 '',
    PRIMARY KEY (`id`),
    KEY              `idx_rdReportId` (`rd_report_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=''report多语言信息表'';


CREATE TABLE `tb_report_matrix_lang`
(
    `id`                  bigint(20) NOT NULL COMMENT '' RD 数据唯一标识 '',
    `rd_report_matrix_id` bigint(20) DEFAULT NULL COMMENT '' RD Report matrix标识 '',
    `language_id`         int(11) DEFAULT NULL COMMENT '' 语言标识 '',
    `evaluation_alias`    varchar(500)  DEFAULT NULL COMMENT '' 测试项别名 '',
    `evaluation_name`     varchar(255)  DEFAULT NULL COMMENT '' 测试项名称 '',
    `citation_name`       varchar(1000) DEFAULT NULL COMMENT '' 测试标准名称 '',
    `citation_full_name`  varchar(255)  DEFAULT NULL COMMENT '' 测试标准全称 '',
    `method_desc`         varchar(512)  DEFAULT NULL,
    `customer_conclusion` varchar(50)   DEFAULT NULL COMMENT '' 客户测试结论 '',
    `created_by`          varchar(50)   DEFAULT NULL COMMENT '' 创建人 '',
    `created_date`        datetime      DEFAULT NULL COMMENT '' 创建时间 '',
    `modified_by`         varchar(50)   DEFAULT NULL COMMENT '' 修改人 '',
    `modified_date`       datetime      DEFAULT NULL COMMENT '' 修改时间 '',
    PRIMARY KEY (`id`),
    KEY                   `idx_rd_report_matrix_id` (`rd_report_matrix_id`),
    KEY                   `idx_createdDate` (`created_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_report_product_dff`
(
    `id`           bigint(20) NOT NULL COMMENT '' RD 唯一标识 '',
    `lab_id`       bigint(20) NOT NULL COMMENT '' Trims系统实验室标识 '',
    `rd_report_id` bigint(20) DEFAULT NULL COMMENT '' report唯一标识 '',
    `order_no`     varchar(50) DEFAULT NULL COMMENT '' 订单号 '',
    `report_no`    varchar(50) NOT NULL COMMENT '' 报告号 '',
    `object_type`  int(11) DEFAULT NULL COMMENT '' 1、Product;2、Sample；'',
                                         `product_instance_id` varchar(36) DEFAULT NULL COMMENT ''产品实例编码'',
                                         `form_id` varchar(50) DEFAULT NULL COMMENT ''DFF Form 标识'',
                                         `language_id` int(11) DEFAULT NULL COMMENT ''语言标识'',
                                         `label_code` varchar(100) CHARACTER SET latin1 DEFAULT NULL COMMENT ''BU 标签编码'',
                                         `label_name` varchar(100) CHARACTER SET latin1 DEFAULT NULL COMMENT ''BU 标签名称'',
                                         `field_code` varchar(50) CHARACTER SET latin1 DEFAULT NULL COMMENT ''DFF 字段编码'',
                                         `customer_label` varchar(50) CHARACTER SET latin1 DEFAULT NULL COMMENT ''客户标签名称'',
                                         `data_type` varchar(50) CHARACTER SET latin1 DEFAULT NULL COMMENT ''数据类型'',
                                         `value` varchar(500) CHARACTER SET utf8 NOT NULL COMMENT ''数值'',
                                         `seq` int(11) DEFAULT NULL COMMENT ''显示顺序'',
                                         `display_in_report` varchar(100) DEFAULT NULL COMMENT ''是否显示在报告'',
                                         PRIMARY KEY (`id`),
                                         KEY `idx_labId_reportNo` (`lab_id`,`report_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=''report product dff信息表'';

CREATE TABLE `tb_report_test_result_lang`
(
    `id`                       bigint(20) NOT NULL COMMENT '' RD 数据唯一标识 '',
    `rd_report_test_result_id` bigint(20) DEFAULT NULL COMMENT '' RD Report Test Result标识 '',
    `language_id`              int(11) DEFAULT NULL COMMENT '' 语言标识 '',
    `test_result_full_name`    varchar(500)  DEFAULT NULL COMMENT '' 测试项目名称，拼接字段 '',
    `result_value_remark`      varchar(500)  DEFAULT NULL COMMENT '' 测试结果备注 '',
    `result_unit`              varchar(1024) DEFAULT NULL COMMENT '' 测试结果单位 '',
    `limit_value_full_name`    varchar(500)  DEFAULT NULL COMMENT '' 参考标准，拼接字段 '',
    `limit_unit`               varchar(4000) DEFAULT NULL COMMENT '' 参考标准单位 '',
    `created_by`               varchar(50)   DEFAULT NULL COMMENT '' 创建人 '',
    `created_date`             datetime NOT NULL COMMENT '' 创建时间 '',
    `modified_by`              varchar(50)   DEFAULT NULL COMMENT '' 修改人 '',
    `modified_date`            datetime NOT NULL COMMENT '' 修改时间 '',
    PRIMARY KEY (`id`),
    KEY                        `idx_rd_report_test_result_id` (`rd_report_test_result_id`),
    KEY                        `idx_createdDate` (`created_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_report_pp_tl_rel`
(
    `id`                      bigint(20) NOT NULL COMMENT '' RD 数据唯一标识 '',
    `rd_report_id`            bigint(20) DEFAULT NULL COMMENT '' RD 报告唯一标识 '',
    `lab_id`                  bigint(20) DEFAULT NULL COMMENT '' Trims系统实验室标识 '',
    `order_no`                varchar(50)  DEFAULT NULL COMMENT '' 订单号 '',
    `report_no`               varchar(50)  DEFAULT NULL COMMENT '' 报告号 '',
    `test_matrix_group_id`    int(11) DEFAULT NULL COMMENT '' 测试单位分组标识 '',
    `test_line_instance_id`   int(11) DEFAULT NULL COMMENT '' 测试项实例标识 '',
    `pp_no`                   int(11) DEFAULT NULL COMMENT '' 测试包编码 '',
    `pp_name`                 varchar(255) DEFAULT NULL COMMENT '' 测试包名称 '',
    `aid`                     bigint(20) DEFAULT NULL COMMENT '' 测试包下测试项标识 '',
    `created_by`              varchar(50)  DEFAULT NULL COMMENT '' 创建人 '',
    `created_date`            datetime NOT NULL COMMENT '' 创建时间 '',
    `modified_by`             varchar(50)  DEFAULT NULL COMMENT '' 修改人 '',
    `modified_date`           datetime NOT NULL COMMENT '' 修改时间 '',
    `last_modified_timestamp` timestamp(3) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '' DB 自动更新，不允许程序设置 '',
    PRIMARY KEY (`id`),
    KEY                       `idx_labId_reportNo` (`lab_id`,`report_no`) USING BTREE,
    KEY                       `idx_createdDate` (`created_date`) USING BTREE,
    KEY                       `idx_rd_report_id` (`rd_report_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_report_trf_rel`
(
    `id`                      bigint(20) NOT NULL COMMENT '' RD 唯一标识 '',
    `lab_id`                  bigint(20) NOT NULL COMMENT '' Trims 实验室标识 '',
    `bu_id`                   bigint(20) DEFAULT NULL COMMENT '' 产品线线标识 '',
    `trf_ref_system_id`       int(11) unsigned NOT NULL DEFAULT '' 0 '' COMMENT '' 测试申请单对接系统标识 '',
    `trf_no`                  varchar(64) NOT NULL DEFAULT '''' COMMENT '' 测试申请单编码 '',
    `order_system_id`         int(11) unsigned NOT NULL COMMENT '' 订单执行系统标识 '',
    `order_no`                varchar(64) NOT NULL COMMENT '' 订单编码 '',
    `report_no`               varchar(64) NOT NULL COMMENT '' 报告编码 '',
    `active_indicator`        tinyint(1) NOT NULL DEFAULT '' 1 '' COMMENT '' 0: inactive, 1
    :
    active
    '',
    `created_by`              varchar(50)          DEFAULT NULL COMMENT '' 创建人 '',
    `created_date`            datetime    NOT NULL COMMENT '' 创建时间 '',
    `modified_by`             varchar(50)          DEFAULT NULL COMMENT '' 修改人 '',
    `modified_date`           datetime    NOT NULL COMMENT '' 修改时间 '',
    `last_modified_timestamp` timestamp(3) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '' DB 自动更新，不允许程序设置 '',
    PRIMARY KEY (`id`),
    KEY                       `idx_trfSysId_trfNo` (`trf_ref_system_id`,`trf_no`) USING BTREE,
    KEY                       `idx_orderSysId_OrderNo` (`order_system_id`,`order_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=''trf report关系表'';
