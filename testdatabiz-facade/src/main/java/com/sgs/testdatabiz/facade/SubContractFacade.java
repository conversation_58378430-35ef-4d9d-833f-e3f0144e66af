package com.sgs.testdatabiz.facade;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.SubTestDataReq;
import com.sgs.testdatabiz.facade.model.req.backsubcontract.SubCompleteTestDataReq;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.InputStreamReq;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.SubcontractNoReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractQueryReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractReplaceReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractInvalidateReq;
import com.sgs.testdatabiz.facade.model.rsp.subcontract.ReportDataRsp;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.List;

public interface SubContractFacade {

//    BaseResponse<XSSFWorkbook> downLoadTemplate(SubcontractNoReq reqObject);
//
//    BaseResponse uploadSubTemplate(InputStreamReq reqObject);

    BaseResponse<ReportDataRsp> querySubTestData(SubcontractNoReq reqObject);

    BaseResponse saveSubCompleteTestData(SubCompleteTestDataReq reqObject);

    BaseResponse cancelSubTestData(SubcontractNoReq reqObject);

    BaseResponse saveEnterSubContractTestData(SubTestDataReq reqObject);

    /**
     * 查询分包数据
     * 
     * 根据订单号、对象编号、实验室代码、报告号查询有效的分包数据
     * 
     * @param req 查询请求参数
     * @return 查询结果
     */
    BaseResponse<List<ReportTestDataInfo>> querySubcontractData(SubcontractQueryReq req);

    /**
     * 替换分包数据ReportNo
     * 
     * 通过原数据无效+新增数据的方式实现ReportNo替换
     * 
     * @param req 替换请求参数
     * @return 操作结果
     */
    BaseResponse<Void> replaceSubcontractReportNo(SubcontractReplaceReq req);

    /**
     * 设置分包数据为无效状态
     * 
     * 通过更新ActiveIndicator字段为0实现软删除
     * 
     * @param req 无效化请求参数
     * @return 操作结果
     */
    BaseResponse<Void> invalidateSubcontractData(SubcontractInvalidateReq req);

}
