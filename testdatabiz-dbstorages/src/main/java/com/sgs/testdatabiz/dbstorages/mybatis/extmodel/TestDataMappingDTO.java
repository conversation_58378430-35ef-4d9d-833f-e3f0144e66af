package com.sgs.testdatabiz.dbstorages.mybatis.extmodel;

import com.sgs.framework.core.common.PrintFriendliness;

/**
 * <AUTHOR>
 * @date 2022/11/30 17:35
 */
public class TestDataMappingDTO extends PrintFriendliness {
    /**
     * TestLineId INTEGER(10) 必填<br>
     *
     */
    private Integer testLineId;

    /**
     * SlimCode VARCHAR(200)<br>
     *
     */
    private String slimCode;

    /**
     * LabCode VARCHAR(64)<br>
     *
     */
    private String labCode;

    /**
     * ProductLineCode VARCHAR(50) 必填<br>
     *
     */
    private String productLineCode;

    /**
     * TestLineEvaluation VARCHAR(500)<br>
     *
     */
    private String testLineEvaluation;

    /**
     * StandardId INTEGER(10)<br>
     *
     */
    private Integer standardId;

    /**
     * StandardName VARCHAR(500)<br>
     *
     */
    private String standardName;

    /**
     * SystemId INTEGER(10)<br>
     * 1:slim   2:fast
     */
    private Integer systemId;

    /**
     * ActiveIndicator BIT 默认值[1] 必填<br>
     * 0: inactive, 1: active
     */
    private Boolean activeIndicator;

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public String getSlimCode() {
        return slimCode;
    }

    public void setSlimCode(String slimCode) {
        this.slimCode = slimCode;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getProductLineCode() {
        return productLineCode;
    }

    public void setProductLineCode(String productLineCode) {
        this.productLineCode = productLineCode;
    }

    public String getTestLineEvaluation() {
        return testLineEvaluation;
    }

    public void setTestLineEvaluation(String testLineEvaluation) {
        this.testLineEvaluation = testLineEvaluation;
    }

    public Integer getStandardId() {
        return standardId;
    }

    public void setStandardId(Integer standardId) {
        this.standardId = standardId;
    }

    public String getStandardName() {
        return standardName;
    }

    public void setStandardName(String standardName) {
        this.standardName = standardName;
    }

    public Integer getSystemId() {
        return systemId;
    }

    public void setSystemId(Integer systemId) {
        this.systemId = systemId;
    }

    public Boolean getActiveIndicator() {
        return activeIndicator;
    }

    public void setActiveIndicator(Boolean activeIndicator) {
        this.activeIndicator = activeIndicator;
    }
}
