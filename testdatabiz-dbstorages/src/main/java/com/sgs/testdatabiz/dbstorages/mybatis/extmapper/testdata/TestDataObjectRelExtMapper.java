package com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.SubcontractQueryDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataObjectRelDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.facade.model.dto.TestDataQueryDTO;
import com.sgs.testdatabiz.facade.model.info.TestDataInfo;
import com.sgs.testdatabiz.facade.model.info.starlims.StarLimsRelData;
import com.sgs.testdatabiz.facade.model.info.starlims.TestDataObjectRelInfo;
import com.sgs.testdatabiz.facade.model.req.TestDataDeleteReq;
import com.sgs.testdatabiz.facade.model.req.starlims.FolderReportInfoReq;
import com.sgs.testdatabiz.facade.model.rsp.subcontract.UploadCitationInfo;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TestDataObjectRelExtMapper {
    /**
     *
     * @param objectRels
     * @return
     */
    Integer batchInsert(@Param("objectRels") List<TestDataObjectRelPO> objectRels);

    TestDataObjectRelPO getReportObjectRelByObjectNo(@Param("objectNo")String objectNo);

    /**
     *
     * @param objectRel
     * @return
     */
    TestDataObjectRelPO getReportObjectRelInfo(TestDataObjectRelPO objectRel);

    Integer updateInvalidOrValid(@Param("objectDto") TestDataObjectRelDTO objectRelDTO);

    List<UploadCitationInfo> queryUploadTestDataBySubcontractNo(@Param("objectNo")String objectNo,@Param("externalObjectNo")String externalObjectNo,  @Param("suffix") String suffix);

    int cancelUploadTestData(@Param("objectNo")String objectNo, @Param("suffix") String suffix);

    TestDataObjectRelPO getReportObjectRelByorderNo(@Param("orderNo")String orderNo);
    List<TestDataObjectRelPO> getReportObjectRelByReportNoAndLabId(@Param("reportNo")String reportNo,@Param("labId")Long labId);

    /**
     *
     * @param folderReport
     * @return
     */
    List<TestDataObjectRelInfo> getStarlimsObjectRelList(FolderReportInfoReq folderReport);

    List<StarLimsRelData> getAbnormalStarlimsData(@Param("suffix") String suffix);

    /**
     * 获取 TestData数据
     * @param queryDTO
     * @return
     */
    List<TestDataInfo> queryTestData(TestDataQueryDTO queryDTO);

    void deleteByObjectNoAndExternalNo(TestDataDeleteReq objectNo);

    /**
     * 查询有效的分包数据
     *
     * 根据订单号、对象编号、实验室代码、报告号查询有效的分包数据记录
     * 用于分包数据查询接口的业务实现
     *
     * @param queryDTO 查询参数DTO，包含订单号、对象编号、实验室代码、报告号
     * @return 查询到的有效分包数据列表
     */
    List<TestDataObjectRelPO> queryValidSubcontractData(@Param("queryDTO") SubcontractQueryDTO queryDTO);

}
