package com.sgs.testdatabiz.dbstorages.mybatis.extmodel;

import com.sgs.framework.core.common.PrintFriendliness;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/1 9:56
 */
@Data
public class TestDataAndMatrixDTO extends PrintFriendliness {

    private String orderNo;

    private String labCode;

    private String parentOrderNo;

    private String objectNo;

    private String testMatrixId;

    private String testDataMatrixId;

    private Integer ppVersionId;

    private Integer aid;

    private String externalCode;

    private String externalNo;

    private Integer testLineId;

    private String evaluationAlias;

    private Integer citationId;

    private Integer citationVersionId;

    private Integer citationType;

    private String citationName;

    private Integer testLineSeq;

    private String matrixLanuges;

    private String methodDesc;

    private String sampleId;

    private String sampleNo;

    private String externalSampleNo;

    private Integer sampleSeq;

    private Integer matrixConclusionId;

    private Integer analyteId;

    private String analyteName;

    private String position;

    private String analyteCode;
    private Integer analyteType;
    private Integer analyteSeq;
    private Integer analyteConclusionId;
    private String reportUnit;
    private String testValue;
    private String casNo;
    private String reportLimit;
    private String limitUnit;

    private Integer testDataConclusionId;

    private Integer sourceType;

    private String testDataLanguages;

    private String testSampleInstanceId;

    private String testLineInstanceId;

    private String testResultInstanceId;
    //SCI-1378
    private String methodLimit;
    private String matrixExtFields;
}
