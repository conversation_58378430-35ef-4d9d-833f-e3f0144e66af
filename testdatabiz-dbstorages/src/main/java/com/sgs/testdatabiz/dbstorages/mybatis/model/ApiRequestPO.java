package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class ApiRequestPO {
    /**
     * id BIGINT(19) 必填<br>
     * id
     */
    private Long id;

    /**
     * system_id INTEGER(10) 必填<br>
     * 调用方系统id
     */
    private Integer systemId;

    /**
     * lab_code VARCHAR(50)<br>
     * lab代码
     */
    private String labCode;

    /**
     * request_id VARCHAR(100) 必填<br>
     * 请求id
     */
    private String requestId;

    /**
     * method_name VARCHAR(100)<br>
     * 请求方法名
     */
    private String methodName;

    /**
     * ext_id VARCHAR(100)<br>
     * 业务id
     */
    private String extId;

    /**
     * request_header OTHER<br>
     * 请求头
     */
    private Object requestHeader;

    /**
     * request_body OTHER<br>
     * 请求body
     */
    private Object requestBody;

    /**
     * response_body OTHER<br>
     * 响应body
     */
    private Object responseBody;

    /**
     * response_status VARCHAR(50)<br>
     * 响应状态码
     */
    private String responseStatus;

    /**
     * created_by VARCHAR(50)<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50)<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * last_modified_timestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP] 必填<br>
     * 最近修改时间
     */
    private Date lastModifiedTimestamp;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 id
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * system_id INTEGER(10) 必填<br>
     * 获得 调用方系统id
     */
    public Integer getSystemId() {
        return systemId;
    }

    /**
     * system_id INTEGER(10) 必填<br>
     * 设置 调用方系统id
     */
    public void setSystemId(Integer systemId) {
        this.systemId = systemId;
    }

    /**
     * lab_code VARCHAR(50)<br>
     * 获得 lab代码
     */
    public String getLabCode() {
        return labCode;
    }

    /**
     * lab_code VARCHAR(50)<br>
     * 设置 lab代码
     */
    public void setLabCode(String labCode) {
        this.labCode = labCode == null ? null : labCode.trim();
    }

    /**
     * request_id VARCHAR(100) 必填<br>
     * 获得 请求id
     */
    public String getRequestId() {
        return requestId;
    }

    /**
     * request_id VARCHAR(100) 必填<br>
     * 设置 请求id
     */
    public void setRequestId(String requestId) {
        this.requestId = requestId == null ? null : requestId.trim();
    }

    /**
     * method_name VARCHAR(100)<br>
     * 获得 请求方法名
     */
    public String getMethodName() {
        return methodName;
    }

    /**
     * method_name VARCHAR(100)<br>
     * 设置 请求方法名
     */
    public void setMethodName(String methodName) {
        this.methodName = methodName == null ? null : methodName.trim();
    }

    /**
     * ext_id VARCHAR(100)<br>
     * 获得 业务id
     */
    public String getExtId() {
        return extId;
    }

    /**
     * ext_id VARCHAR(100)<br>
     * 设置 业务id
     */
    public void setExtId(String extId) {
        this.extId = extId == null ? null : extId.trim();
    }

    /**
     * request_header OTHER<br>
     * 获得 请求头
     */
    public Object getRequestHeader() {
        return requestHeader;
    }

    /**
     * request_header OTHER<br>
     * 设置 请求头
     */
    public void setRequestHeader(Object requestHeader) {
        this.requestHeader = requestHeader;
    }

    /**
     * request_body OTHER<br>
     * 获得 请求body
     */
    public Object getRequestBody() {
        return requestBody;
    }

    /**
     * request_body OTHER<br>
     * 设置 请求body
     */
    public void setRequestBody(Object requestBody) {
        this.requestBody = requestBody;
    }

    /**
     * response_body OTHER<br>
     * 获得 响应body
     */
    public Object getResponseBody() {
        return responseBody;
    }

    /**
     * response_body OTHER<br>
     * 设置 响应body
     */
    public void setResponseBody(Object responseBody) {
        this.responseBody = responseBody;
    }

    /**
     * response_status VARCHAR(50)<br>
     * 获得 响应状态码
     */
    public String getResponseStatus() {
        return responseStatus;
    }

    /**
     * response_status VARCHAR(50)<br>
     * 设置 响应状态码
     */
    public void setResponseStatus(String responseStatus) {
        this.responseStatus = responseStatus == null ? null : responseStatus.trim();
    }

    /**
     * created_by VARCHAR(50)<br>
     * 获得 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 设置 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19) 必填<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 获得 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 设置 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19) 必填<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP] 必填<br>
     * 获得 最近修改时间
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP] 必填<br>
     * 设置 最近修改时间
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", systemId=").append(systemId);
        sb.append(", labCode=").append(labCode);
        sb.append(", requestId=").append(requestId);
        sb.append(", methodName=").append(methodName);
        sb.append(", extId=").append(extId);
        sb.append(", requestHeader=").append(requestHeader);
        sb.append(", requestBody=").append(requestBody);
        sb.append(", responseBody=").append(responseBody);
        sb.append(", responseStatus=").append(responseStatus);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append("]");
        return sb.toString();
    }
}