package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ApiRequestExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ApiRequestExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNull() {
            addCriterion("system_id is null");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNotNull() {
            addCriterion("system_id is not null");
            return (Criteria) this;
        }

        public Criteria andSystemIdEqualTo(Integer value) {
            addCriterion("system_id =", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotEqualTo(Integer value) {
            addCriterion("system_id <>", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThan(Integer value) {
            addCriterion("system_id >", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("system_id >=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThan(Integer value) {
            addCriterion("system_id <", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThanOrEqualTo(Integer value) {
            addCriterion("system_id <=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIn(List<Integer> values) {
            addCriterion("system_id in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotIn(List<Integer> values) {
            addCriterion("system_id not in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdBetween(Integer value1, Integer value2) {
            addCriterion("system_id between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotBetween(Integer value1, Integer value2) {
            addCriterion("system_id not between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andLabCodeIsNull() {
            addCriterion("lab_code is null");
            return (Criteria) this;
        }

        public Criteria andLabCodeIsNotNull() {
            addCriterion("lab_code is not null");
            return (Criteria) this;
        }

        public Criteria andLabCodeEqualTo(String value) {
            addCriterion("lab_code =", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotEqualTo(String value) {
            addCriterion("lab_code <>", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeGreaterThan(String value) {
            addCriterion("lab_code >", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeGreaterThanOrEqualTo(String value) {
            addCriterion("lab_code >=", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLessThan(String value) {
            addCriterion("lab_code <", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLessThanOrEqualTo(String value) {
            addCriterion("lab_code <=", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLike(String value) {
            addCriterion("lab_code like", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotLike(String value) {
            addCriterion("lab_code not like", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeIn(List<String> values) {
            addCriterion("lab_code in", values, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotIn(List<String> values) {
            addCriterion("lab_code not in", values, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeBetween(String value1, String value2) {
            addCriterion("lab_code between", value1, value2, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotBetween(String value1, String value2) {
            addCriterion("lab_code not between", value1, value2, "labCode");
            return (Criteria) this;
        }

        public Criteria andRequestIdIsNull() {
            addCriterion("request_id is null");
            return (Criteria) this;
        }

        public Criteria andRequestIdIsNotNull() {
            addCriterion("request_id is not null");
            return (Criteria) this;
        }

        public Criteria andRequestIdEqualTo(String value) {
            addCriterion("request_id =", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotEqualTo(String value) {
            addCriterion("request_id <>", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdGreaterThan(String value) {
            addCriterion("request_id >", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdGreaterThanOrEqualTo(String value) {
            addCriterion("request_id >=", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdLessThan(String value) {
            addCriterion("request_id <", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdLessThanOrEqualTo(String value) {
            addCriterion("request_id <=", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdLike(String value) {
            addCriterion("request_id like", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotLike(String value) {
            addCriterion("request_id not like", value, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdIn(List<String> values) {
            addCriterion("request_id in", values, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotIn(List<String> values) {
            addCriterion("request_id not in", values, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdBetween(String value1, String value2) {
            addCriterion("request_id between", value1, value2, "requestId");
            return (Criteria) this;
        }

        public Criteria andRequestIdNotBetween(String value1, String value2) {
            addCriterion("request_id not between", value1, value2, "requestId");
            return (Criteria) this;
        }

        public Criteria andMethodNameIsNull() {
            addCriterion("method_name is null");
            return (Criteria) this;
        }

        public Criteria andMethodNameIsNotNull() {
            addCriterion("method_name is not null");
            return (Criteria) this;
        }

        public Criteria andMethodNameEqualTo(String value) {
            addCriterion("method_name =", value, "methodName");
            return (Criteria) this;
        }

        public Criteria andMethodNameNotEqualTo(String value) {
            addCriterion("method_name <>", value, "methodName");
            return (Criteria) this;
        }

        public Criteria andMethodNameGreaterThan(String value) {
            addCriterion("method_name >", value, "methodName");
            return (Criteria) this;
        }

        public Criteria andMethodNameGreaterThanOrEqualTo(String value) {
            addCriterion("method_name >=", value, "methodName");
            return (Criteria) this;
        }

        public Criteria andMethodNameLessThan(String value) {
            addCriterion("method_name <", value, "methodName");
            return (Criteria) this;
        }

        public Criteria andMethodNameLessThanOrEqualTo(String value) {
            addCriterion("method_name <=", value, "methodName");
            return (Criteria) this;
        }

        public Criteria andMethodNameLike(String value) {
            addCriterion("method_name like", value, "methodName");
            return (Criteria) this;
        }

        public Criteria andMethodNameNotLike(String value) {
            addCriterion("method_name not like", value, "methodName");
            return (Criteria) this;
        }

        public Criteria andMethodNameIn(List<String> values) {
            addCriterion("method_name in", values, "methodName");
            return (Criteria) this;
        }

        public Criteria andMethodNameNotIn(List<String> values) {
            addCriterion("method_name not in", values, "methodName");
            return (Criteria) this;
        }

        public Criteria andMethodNameBetween(String value1, String value2) {
            addCriterion("method_name between", value1, value2, "methodName");
            return (Criteria) this;
        }

        public Criteria andMethodNameNotBetween(String value1, String value2) {
            addCriterion("method_name not between", value1, value2, "methodName");
            return (Criteria) this;
        }

        public Criteria andExtIdIsNull() {
            addCriterion("ext_id is null");
            return (Criteria) this;
        }

        public Criteria andExtIdIsNotNull() {
            addCriterion("ext_id is not null");
            return (Criteria) this;
        }

        public Criteria andExtIdEqualTo(String value) {
            addCriterion("ext_id =", value, "extId");
            return (Criteria) this;
        }

        public Criteria andExtIdNotEqualTo(String value) {
            addCriterion("ext_id <>", value, "extId");
            return (Criteria) this;
        }

        public Criteria andExtIdGreaterThan(String value) {
            addCriterion("ext_id >", value, "extId");
            return (Criteria) this;
        }

        public Criteria andExtIdGreaterThanOrEqualTo(String value) {
            addCriterion("ext_id >=", value, "extId");
            return (Criteria) this;
        }

        public Criteria andExtIdLessThan(String value) {
            addCriterion("ext_id <", value, "extId");
            return (Criteria) this;
        }

        public Criteria andExtIdLessThanOrEqualTo(String value) {
            addCriterion("ext_id <=", value, "extId");
            return (Criteria) this;
        }

        public Criteria andExtIdLike(String value) {
            addCriterion("ext_id like", value, "extId");
            return (Criteria) this;
        }

        public Criteria andExtIdNotLike(String value) {
            addCriterion("ext_id not like", value, "extId");
            return (Criteria) this;
        }

        public Criteria andExtIdIn(List<String> values) {
            addCriterion("ext_id in", values, "extId");
            return (Criteria) this;
        }

        public Criteria andExtIdNotIn(List<String> values) {
            addCriterion("ext_id not in", values, "extId");
            return (Criteria) this;
        }

        public Criteria andExtIdBetween(String value1, String value2) {
            addCriterion("ext_id between", value1, value2, "extId");
            return (Criteria) this;
        }

        public Criteria andExtIdNotBetween(String value1, String value2) {
            addCriterion("ext_id not between", value1, value2, "extId");
            return (Criteria) this;
        }

        public Criteria andRequestHeaderIsNull() {
            addCriterion("request_header is null");
            return (Criteria) this;
        }

        public Criteria andRequestHeaderIsNotNull() {
            addCriterion("request_header is not null");
            return (Criteria) this;
        }

        public Criteria andRequestHeaderEqualTo(Object value) {
            addCriterion("request_header =", value, "requestHeader");
            return (Criteria) this;
        }

        public Criteria andRequestHeaderNotEqualTo(Object value) {
            addCriterion("request_header <>", value, "requestHeader");
            return (Criteria) this;
        }

        public Criteria andRequestHeaderGreaterThan(Object value) {
            addCriterion("request_header >", value, "requestHeader");
            return (Criteria) this;
        }

        public Criteria andRequestHeaderGreaterThanOrEqualTo(Object value) {
            addCriterion("request_header >=", value, "requestHeader");
            return (Criteria) this;
        }

        public Criteria andRequestHeaderLessThan(Object value) {
            addCriterion("request_header <", value, "requestHeader");
            return (Criteria) this;
        }

        public Criteria andRequestHeaderLessThanOrEqualTo(Object value) {
            addCriterion("request_header <=", value, "requestHeader");
            return (Criteria) this;
        }

        public Criteria andRequestHeaderIn(List<Object> values) {
            addCriterion("request_header in", values, "requestHeader");
            return (Criteria) this;
        }

        public Criteria andRequestHeaderNotIn(List<Object> values) {
            addCriterion("request_header not in", values, "requestHeader");
            return (Criteria) this;
        }

        public Criteria andRequestHeaderBetween(Object value1, Object value2) {
            addCriterion("request_header between", value1, value2, "requestHeader");
            return (Criteria) this;
        }

        public Criteria andRequestHeaderNotBetween(Object value1, Object value2) {
            addCriterion("request_header not between", value1, value2, "requestHeader");
            return (Criteria) this;
        }

        public Criteria andRequestBodyIsNull() {
            addCriterion("request_body is null");
            return (Criteria) this;
        }

        public Criteria andRequestBodyIsNotNull() {
            addCriterion("request_body is not null");
            return (Criteria) this;
        }

        public Criteria andRequestBodyEqualTo(Object value) {
            addCriterion("request_body =", value, "requestBody");
            return (Criteria) this;
        }

        public Criteria andRequestBodyNotEqualTo(Object value) {
            addCriterion("request_body <>", value, "requestBody");
            return (Criteria) this;
        }

        public Criteria andRequestBodyGreaterThan(Object value) {
            addCriterion("request_body >", value, "requestBody");
            return (Criteria) this;
        }

        public Criteria andRequestBodyGreaterThanOrEqualTo(Object value) {
            addCriterion("request_body >=", value, "requestBody");
            return (Criteria) this;
        }

        public Criteria andRequestBodyLessThan(Object value) {
            addCriterion("request_body <", value, "requestBody");
            return (Criteria) this;
        }

        public Criteria andRequestBodyLessThanOrEqualTo(Object value) {
            addCriterion("request_body <=", value, "requestBody");
            return (Criteria) this;
        }

        public Criteria andRequestBodyIn(List<Object> values) {
            addCriterion("request_body in", values, "requestBody");
            return (Criteria) this;
        }

        public Criteria andRequestBodyNotIn(List<Object> values) {
            addCriterion("request_body not in", values, "requestBody");
            return (Criteria) this;
        }

        public Criteria andRequestBodyBetween(Object value1, Object value2) {
            addCriterion("request_body between", value1, value2, "requestBody");
            return (Criteria) this;
        }

        public Criteria andRequestBodyNotBetween(Object value1, Object value2) {
            addCriterion("request_body not between", value1, value2, "requestBody");
            return (Criteria) this;
        }

        public Criteria andResponseBodyIsNull() {
            addCriterion("response_body is null");
            return (Criteria) this;
        }

        public Criteria andResponseBodyIsNotNull() {
            addCriterion("response_body is not null");
            return (Criteria) this;
        }

        public Criteria andResponseBodyEqualTo(Object value) {
            addCriterion("response_body =", value, "responseBody");
            return (Criteria) this;
        }

        public Criteria andResponseBodyNotEqualTo(Object value) {
            addCriterion("response_body <>", value, "responseBody");
            return (Criteria) this;
        }

        public Criteria andResponseBodyGreaterThan(Object value) {
            addCriterion("response_body >", value, "responseBody");
            return (Criteria) this;
        }

        public Criteria andResponseBodyGreaterThanOrEqualTo(Object value) {
            addCriterion("response_body >=", value, "responseBody");
            return (Criteria) this;
        }

        public Criteria andResponseBodyLessThan(Object value) {
            addCriterion("response_body <", value, "responseBody");
            return (Criteria) this;
        }

        public Criteria andResponseBodyLessThanOrEqualTo(Object value) {
            addCriterion("response_body <=", value, "responseBody");
            return (Criteria) this;
        }

        public Criteria andResponseBodyIn(List<Object> values) {
            addCriterion("response_body in", values, "responseBody");
            return (Criteria) this;
        }

        public Criteria andResponseBodyNotIn(List<Object> values) {
            addCriterion("response_body not in", values, "responseBody");
            return (Criteria) this;
        }

        public Criteria andResponseBodyBetween(Object value1, Object value2) {
            addCriterion("response_body between", value1, value2, "responseBody");
            return (Criteria) this;
        }

        public Criteria andResponseBodyNotBetween(Object value1, Object value2) {
            addCriterion("response_body not between", value1, value2, "responseBody");
            return (Criteria) this;
        }

        public Criteria andResponseStatusIsNull() {
            addCriterion("response_status is null");
            return (Criteria) this;
        }

        public Criteria andResponseStatusIsNotNull() {
            addCriterion("response_status is not null");
            return (Criteria) this;
        }

        public Criteria andResponseStatusEqualTo(String value) {
            addCriterion("response_status =", value, "responseStatus");
            return (Criteria) this;
        }

        public Criteria andResponseStatusNotEqualTo(String value) {
            addCriterion("response_status <>", value, "responseStatus");
            return (Criteria) this;
        }

        public Criteria andResponseStatusGreaterThan(String value) {
            addCriterion("response_status >", value, "responseStatus");
            return (Criteria) this;
        }

        public Criteria andResponseStatusGreaterThanOrEqualTo(String value) {
            addCriterion("response_status >=", value, "responseStatus");
            return (Criteria) this;
        }

        public Criteria andResponseStatusLessThan(String value) {
            addCriterion("response_status <", value, "responseStatus");
            return (Criteria) this;
        }

        public Criteria andResponseStatusLessThanOrEqualTo(String value) {
            addCriterion("response_status <=", value, "responseStatus");
            return (Criteria) this;
        }

        public Criteria andResponseStatusLike(String value) {
            addCriterion("response_status like", value, "responseStatus");
            return (Criteria) this;
        }

        public Criteria andResponseStatusNotLike(String value) {
            addCriterion("response_status not like", value, "responseStatus");
            return (Criteria) this;
        }

        public Criteria andResponseStatusIn(List<String> values) {
            addCriterion("response_status in", values, "responseStatus");
            return (Criteria) this;
        }

        public Criteria andResponseStatusNotIn(List<String> values) {
            addCriterion("response_status not in", values, "responseStatus");
            return (Criteria) this;
        }

        public Criteria andResponseStatusBetween(String value1, String value2) {
            addCriterion("response_status between", value1, value2, "responseStatus");
            return (Criteria) this;
        }

        public Criteria andResponseStatusNotBetween(String value1, String value2) {
            addCriterion("response_status not between", value1, value2, "responseStatus");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("created_date is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("created_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("created_date =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("created_date <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("created_date >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("created_date >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("created_date <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("created_date <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("created_date in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("created_date not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("created_date between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("created_date not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("modified_by is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("modified_by is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("modified_by =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("modified_by <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("modified_by >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("modified_by >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("modified_by <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("modified_by <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("modified_by like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("modified_by not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("modified_by in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("modified_by not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("modified_by between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("modified_by not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("modified_date is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("modified_date is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("modified_date =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("modified_date <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("modified_date >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("modified_date >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("modified_date <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("modified_date <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("modified_date in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("modified_date not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("modified_date between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("modified_date not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNull() {
            addCriterion("last_modified_timestamp is null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIsNotNull() {
            addCriterion("last_modified_timestamp is not null");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampEqualTo(Date value) {
            addCriterion("last_modified_timestamp =", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotEqualTo(Date value) {
            addCriterion("last_modified_timestamp <>", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThan(Date value) {
            addCriterion("last_modified_timestamp >", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampGreaterThanOrEqualTo(Date value) {
            addCriterion("last_modified_timestamp >=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThan(Date value) {
            addCriterion("last_modified_timestamp <", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampLessThanOrEqualTo(Date value) {
            addCriterion("last_modified_timestamp <=", value, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampIn(List<Date> values) {
            addCriterion("last_modified_timestamp in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotIn(List<Date> values) {
            addCriterion("last_modified_timestamp not in", values, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampBetween(Date value1, Date value2) {
            addCriterion("last_modified_timestamp between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }

        public Criteria andLastModifiedTimestampNotBetween(Date value1, Date value2) {
            addCriterion("last_modified_timestamp not between", value1, value2, "lastModifiedTimestamp");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}