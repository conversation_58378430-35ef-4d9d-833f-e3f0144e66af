package com.sgs.testdatabiz.dbstorages.mybatis.extmodel;

import com.sgs.framework.core.common.PrintFriendliness;

import java.util.Date;

public class TestDataOldDTO extends PrintFriendliness {

    /**
     * OrderNo VARCHAR(36) 必填<br>
     *
     */
    private String orderNo;

    /**
     * ObjectNo VARCHAR(36)<br>
     *
     */
    private String objectNo;

    /**
     * ExternalNo VARCHAR(30) 必填<br>
     *
     */
    private String externalNo;

    /**
     * ExternalCode VARCHAR(256)<br>
     *
     */
    private String externalCode;

    /**
     * SampleNo VARCHAR(36)<br>
     *
     */
    private String sampleNo;

    /**
     * ExternalSampleNo VARCHAR(50)<br>
     *
     */
    private String externalSampleNo;

    /**
     * ReportLimit VARCHAR(50)<br>
     *
     */
    private String reportLimit;

    /**
     * AnalyteCode VARCHAR(50)<br>
     *
     */
    private String analyteCode;

    /**
     * AnalyteType INTEGER(10) 默认值[0] 必填<br>
     * 0：General、1：Conclusion
     */
    private Integer analyteType;

    /**
     * TestAnalyteName VARCHAR(255)<br>
     *
     */
    private String testAnalyteName;

    /**
     * TestAnalyteNameCN VARCHAR(255)<br>
     *
     */
    private String testAnalyteNameCN;

    /**
     * ReportUnit VARCHAR(255)<br>
     *
     */
    private String reportUnit;

    /**
     * ReportUnitCN VARCHAR(255)<br>
     *
     */
    private String reportUnitCN;

    /**
     * TestValue VARCHAR(150)<br>
     *
     */
    private String testValue;

    /**
     * AnalyteSeq INTEGER(10)<br>
     *
     */
    private Integer analyteSeq;

    /**
     * MaterialName VARCHAR(500)<br>
     * materialName
     */
    private String materialName;

    /**
     * MaterialTexture VARCHAR(255)<br>
     * materialTexture
     */
    private String materialTexture;

    /**
     * UsedPosition VARCHAR(255)<br>
     * usedPosition
     */
    private String usedPosition;

    /**
     * MaterialColor VARCHAR(255)<br>
     * materialColor
     */
    private String materialColor;

    /**
     * SystemId INTEGER(10)<br>
     * 1：Slim、2：Fast
     */
    private Integer systemId;

    private Boolean activeIndicator;

    private Date createdDate;

    private Date modifiedDate;

    /**
     *
     */
    private Integer testLineId;
    /**
     *
     */
    private Integer citationId;
    /**
     *
     */
    private Integer citationVersionId;



    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getObjectNo() {
        return objectNo;
    }

    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo;
    }

    public String getExternalNo() {
        return externalNo;
    }

    public void setExternalNo(String externalNo) {
        this.externalNo = externalNo;
    }

    public String getExternalCode() {
        return externalCode;
    }

    public void setExternalCode(String externalCode) {
        this.externalCode = externalCode;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getExternalSampleNo() {
        return externalSampleNo;
    }

    public void setExternalSampleNo(String externalSampleNo) {
        this.externalSampleNo = externalSampleNo;
    }

    public String getReportLimit() {
        return reportLimit;
    }

    public void setReportLimit(String reportLimit) {
        this.reportLimit = reportLimit;
    }

    public String getAnalyteCode() {
        return analyteCode;
    }

    public void setAnalyteCode(String analyteCode) {
        this.analyteCode = analyteCode;
    }

    public Integer getAnalyteType() {
        return analyteType;
    }

    public void setAnalyteType(Integer analyteType) {
        this.analyteType = analyteType;
    }

    public String getTestAnalyteName() {
        return testAnalyteName;
    }

    public void setTestAnalyteName(String testAnalyteName) {
        this.testAnalyteName = testAnalyteName;
    }

    public String getTestAnalyteNameCN() {
        return testAnalyteNameCN;
    }

    public void setTestAnalyteNameCN(String testAnalyteNameCN) {
        this.testAnalyteNameCN = testAnalyteNameCN;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public String getReportUnitCN() {
        return reportUnitCN;
    }

    public void setReportUnitCN(String reportUnitCN) {
        this.reportUnitCN = reportUnitCN;
    }

    public String getTestValue() {
        return testValue;
    }

    public void setTestValue(String testValue) {
        this.testValue = testValue;
    }

    public Integer getAnalyteSeq() {
        return analyteSeq;
    }

    public void setAnalyteSeq(Integer analyteSeq) {
        this.analyteSeq = analyteSeq;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialTexture() {
        return materialTexture;
    }

    public void setMaterialTexture(String materialTexture) {
        this.materialTexture = materialTexture;
    }

    public String getUsedPosition() {
        return usedPosition;
    }

    public void setUsedPosition(String usedPosition) {
        this.usedPosition = usedPosition;
    }

    public String getMaterialColor() {
        return materialColor;
    }

    public void setMaterialColor(String materialColor) {
        this.materialColor = materialColor;
    }

    public Integer getSystemId() {
        return systemId;
    }

    public void setSystemId(Integer systemId) {
        this.systemId = systemId;
    }

    public Boolean getActiveIndicator() {
        return activeIndicator;
    }

    public void setActiveIndicator(Boolean activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getModifiedDate() {
        return modifiedDate;
    }

    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    public Integer getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    public Integer getCitationId() {
        return citationId;
    }

    public void setCitationId(Integer citationId) {
        this.citationId = citationId;
    }

    public Integer getCitationVersionId() {
        return citationVersionId;
    }

    public void setCitationVersionId(Integer citationVersionId) {
        this.citationVersionId = citationVersionId;
    }
}
