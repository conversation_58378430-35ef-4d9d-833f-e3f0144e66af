package com.sgs.testdatabiz.dbstorages.mybatis.model;

import java.util.Date;

public class TestDataObjectRelPO {
    /**
     * Id VARCHAR(36) 必填<br>
     * 主键Id
     */
    private String id;

    /**
     * ProductLineCode VARCHAR(10) 必填<br>
     * 订单的BU
     */
    private String productLineCode;

    /**
     * LabCode VARCHAR(50) 必填<br>
     * 订单所在的Lab
     */
    private String labCode;

    /**
     * OrderNo VARCHAR(50) 必填<br>
     * 订单号
     */
    private String orderNo;

    /**
     * ParentOrderNo VARCHAR(50)<br>
     * 发包方订单号
     */
    private String parentOrderNo;

    /**
     * ReportNo VARCHAR(50)<br>
     * 报告号
     */
    private String reportNo;

    /**
     * ObjectNo VARCHAR(50)<br>
     * 1、当Slim数据时，该值为SubContractNo
2、当Fast数据时，该值为JobNo
3、当Starlims数据时，该值为SubContractNo
     */
    private String objectNo;

    /**
     * ExternalId VARCHAR(50)<br>
     * 外部系统主键Id
     */
    private String externalId;

    /**
     * ExternalNo VARCHAR(50)<br>
     * 1、当Slim数据时，该值为SlimJobNo
2、当Fast数据时，该值为JobNo
3、当Starlims数据时，该值为folderNo
     */
    private String externalNo;

    /**
     * ExternalObjectNo VARCHAR(50)<br>
     * 
     */
    private String externalObjectNo;

    /**
     * ExcludeCustomerInterface VARCHAR(10)<br>
     * 标记是否回传客户接口，0-回传，1-不回传
     */
    private String excludeCustomerInterface;

    /**
     * SourceType INTEGER(10) 必填<br>
     * 1、slim 2、job 3、starlims 4、fast 5、subcontract 6、Enter
     */
    private Integer sourceType;

    /**
     * LanguageId INTEGER(10) 默认值[0] 必填<br>
     * 
     */
    private Integer languageId;

    /**
     * CompleteDate TIMESTAMP(19)<br>
     * CompleteDate
     */
    private Date completeDate;

    /**
     * BizVersionId CHAR(32) 必填<br>
     * 
     */
    private String bizVersionId;

    /**
     * ActiveIndicator INTEGER(10) 必填<br>
     * 0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * CreatedBy VARCHAR(50)<br>
     * 
     */
    private String createdBy;

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 
     */
    private Date createdDate;

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 
     */
    private String modifiedBy;

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 
     */
    private Date modifiedDate;

    /**
     * LastModifiedTimestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP(3)]<br>
     * 
     */
    private Date lastModifiedTimestamp;

    /**
     * LabId BIGINT(19)<br>
     * 
     */
    private Long labId;

    /**
     * Id VARCHAR(36) 必填<br>
     * 获得 主键Id
     */
    public String getId() {
        return id;
    }

    /**
     * Id VARCHAR(36) 必填<br>
     * 设置 主键Id
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * ProductLineCode VARCHAR(10) 必填<br>
     * 获得 订单的BU
     */
    public String getProductLineCode() {
        return productLineCode;
    }

    /**
     * ProductLineCode VARCHAR(10) 必填<br>
     * 设置 订单的BU
     */
    public void setProductLineCode(String productLineCode) {
        this.productLineCode = productLineCode == null ? null : productLineCode.trim();
    }

    /**
     * LabCode VARCHAR(50) 必填<br>
     * 获得 订单所在的Lab
     */
    public String getLabCode() {
        return labCode;
    }

    /**
     * LabCode VARCHAR(50) 必填<br>
     * 设置 订单所在的Lab
     */
    public void setLabCode(String labCode) {
        this.labCode = labCode == null ? null : labCode.trim();
    }

    /**
     * OrderNo VARCHAR(50) 必填<br>
     * 获得 订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * OrderNo VARCHAR(50) 必填<br>
     * 设置 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * ParentOrderNo VARCHAR(50)<br>
     * 获得 发包方订单号
     */
    public String getParentOrderNo() {
        return parentOrderNo;
    }

    /**
     * ParentOrderNo VARCHAR(50)<br>
     * 设置 发包方订单号
     */
    public void setParentOrderNo(String parentOrderNo) {
        this.parentOrderNo = parentOrderNo == null ? null : parentOrderNo.trim();
    }

    /**
     * ReportNo VARCHAR(50)<br>
     * 获得 报告号
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * ReportNo VARCHAR(50)<br>
     * 设置 报告号
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * ObjectNo VARCHAR(50)<br>
     * 获得 1、当Slim数据时，该值为SubContractNo
2、当Fast数据时，该值为JobNo
3、当Starlims数据时，该值为SubContractNo
     */
    public String getObjectNo() {
        return objectNo;
    }

    /**
     * ObjectNo VARCHAR(50)<br>
     * 设置 1、当Slim数据时，该值为SubContractNo
2、当Fast数据时，该值为JobNo
3、当Starlims数据时，该值为SubContractNo
     */
    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo == null ? null : objectNo.trim();
    }

    /**
     * ExternalId VARCHAR(50)<br>
     * 获得 外部系统主键Id
     */
    public String getExternalId() {
        return externalId;
    }

    /**
     * ExternalId VARCHAR(50)<br>
     * 设置 外部系统主键Id
     */
    public void setExternalId(String externalId) {
        this.externalId = externalId == null ? null : externalId.trim();
    }

    /**
     * ExternalNo VARCHAR(50)<br>
     * 获得 1、当Slim数据时，该值为SlimJobNo
2、当Fast数据时，该值为JobNo
3、当Starlims数据时，该值为folderNo
     */
    public String getExternalNo() {
        return externalNo;
    }

    /**
     * ExternalNo VARCHAR(50)<br>
     * 设置 1、当Slim数据时，该值为SlimJobNo
2、当Fast数据时，该值为JobNo
3、当Starlims数据时，该值为folderNo
     */
    public void setExternalNo(String externalNo) {
        this.externalNo = externalNo == null ? null : externalNo.trim();
    }

    /**
     * ExternalObjectNo VARCHAR(50)<br>
     * 获得 
     */
    public String getExternalObjectNo() {
        return externalObjectNo;
    }

    /**
     * ExternalObjectNo VARCHAR(50)<br>
     * 设置 
     */
    public void setExternalObjectNo(String externalObjectNo) {
        this.externalObjectNo = externalObjectNo == null ? null : externalObjectNo.trim();
    }

    /**
     * ExcludeCustomerInterface VARCHAR(10)<br>
     * 获得 标记是否回传客户接口，0-回传，1-不回传
     */
    public String getExcludeCustomerInterface() {
        return excludeCustomerInterface;
    }

    /**
     * ExcludeCustomerInterface VARCHAR(10)<br>
     * 设置 标记是否回传客户接口，0-回传，1-不回传
     */
    public void setExcludeCustomerInterface(String excludeCustomerInterface) {
        this.excludeCustomerInterface = excludeCustomerInterface == null ? null : excludeCustomerInterface.trim();
    }

    /**
     * SourceType INTEGER(10) 必填<br>
     * 获得 1、slim 2、job 3、starlims 4、fast 5、subcontract 6、Enter
     */
    public Integer getSourceType() {
        return sourceType;
    }

    /**
     * SourceType INTEGER(10) 必填<br>
     * 设置 1、slim 2、job 3、starlims 4、fast 5、subcontract 6、Enter
     */
    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    /**
     * LanguageId INTEGER(10) 默认值[0] 必填<br>
     * 获得 
     */
    public Integer getLanguageId() {
        return languageId;
    }

    /**
     * LanguageId INTEGER(10) 默认值[0] 必填<br>
     * 设置 
     */
    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    /**
     * CompleteDate TIMESTAMP(19)<br>
     * 获得 CompleteDate
     */
    public Date getCompleteDate() {
        return completeDate;
    }

    /**
     * CompleteDate TIMESTAMP(19)<br>
     * 设置 CompleteDate
     */
    public void setCompleteDate(Date completeDate) {
        this.completeDate = completeDate;
    }

    /**
     * BizVersionId CHAR(32) 必填<br>
     * 获得 
     */
    public String getBizVersionId() {
        return bizVersionId;
    }

    /**
     * BizVersionId CHAR(32) 必填<br>
     * 设置 
     */
    public void setBizVersionId(String bizVersionId) {
        this.bizVersionId = bizVersionId == null ? null : bizVersionId.trim();
    }

    /**
     * ActiveIndicator INTEGER(10) 必填<br>
     * 获得 0: inactive, 1: active
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * ActiveIndicator INTEGER(10) 必填<br>
     * 设置 0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 获得 
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 设置 
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 设置 
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 获得 
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 设置 
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 设置 
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * LastModifiedTimestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP(3)]<br>
     * 获得 
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * LastModifiedTimestamp TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP(3)]<br>
     * 设置 
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    /**
     * LabId BIGINT(19)<br>
     * 获得 
     */
    public Long getLabId() {
        return labId;
    }

    /**
     * LabId BIGINT(19)<br>
     * 设置 
     */
    public void setLabId(Long labId) {
        this.labId = labId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", productLineCode=").append(productLineCode);
        sb.append(", labCode=").append(labCode);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", parentOrderNo=").append(parentOrderNo);
        sb.append(", reportNo=").append(reportNo);
        sb.append(", objectNo=").append(objectNo);
        sb.append(", externalId=").append(externalId);
        sb.append(", externalNo=").append(externalNo);
        sb.append(", externalObjectNo=").append(externalObjectNo);
        sb.append(", excludeCustomerInterface=").append(excludeCustomerInterface);
        sb.append(", sourceType=").append(sourceType);
        sb.append(", languageId=").append(languageId);
        sb.append(", completeDate=").append(completeDate);
        sb.append(", bizVersionId=").append(bizVersionId);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append(", labId=").append(labId);
        sb.append("]");
        return sb.toString();
    }
}