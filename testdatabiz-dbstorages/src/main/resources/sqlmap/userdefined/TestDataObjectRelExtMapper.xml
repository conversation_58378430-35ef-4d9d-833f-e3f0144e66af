<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataObjectRelExtMapper" >
    <resultMap id="BaseResultMap" type="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO" >
        <id column="Id" property="id" jdbcType="VARCHAR" />
        <result column="ProductLineCode" property="productLineCode" jdbcType="VARCHAR" />
        <result column="LabCode" property="labCode" jdbcType="VARCHAR" />
        <result column="OrderNo" property="orderNo" jdbcType="VARCHAR" />
        <result column="ParentOrderNo" property="parentOrderNo" jdbcType="VARCHAR" />
        <result column="ReportNo" property="reportNo" jdbcType="VARCHAR" />
        <result column="ObjectNo" property="objectNo" jdbcType="VARCHAR" />
        <result column="ExternalId" property="externalId" jdbcType="VARCHAR" />
        <result column="ExternalNo" property="externalNo" jdbcType="VARCHAR" />
        <result column="ExternalObjectNo" property="externalObjectNo" jdbcType="VARCHAR" />
        <result column="SourceType" property="sourceType" jdbcType="INTEGER" />
        <result column="LanguageId" property="languageId" jdbcType="INTEGER" />
        <result column="CompleteDate" property="completeDate" />
        <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
        <result column="ExcludeCustomerInterface" property="excludeCustomerInterface" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="uploadTestDataMap" type="com.sgs.testdatabiz.facade.model.rsp.subcontract.UploadCitationInfo">
        <result column="citationName" property="citationName" jdbcType="VARCHAR"/>
        <result column="testLineId" property="testLineId" />
        <result column="evaluationAlias" property="evaluationAlias" jdbcType="VARCHAR" />
        <collection property="citations"  ofType="com.sgs.testdatabiz.facade.model.rsp.subcontract.UploadTestInfo">
            <result column="analyteName" property="analyteName" jdbcType="VARCHAR" />
            <result column="reportUnit" property="reportUnit" jdbcType="VARCHAR" />
            <result column="reportLimit" property="reportLimit" jdbcType="VARCHAR" />
            <result column="sampleNo" property="sampleNo" jdbcType="VARCHAR"/>
            <result column="testValue" property="testValue" jdbcType="VARCHAR" />
            <result column="analyteSeq" property="analyteSeq" />
            <result column="sampleSeq" property="sampleSeq" />
            <result column="languages" property="languages" />
        </collection>
    </resultMap>

    <sql id="Base_Column_List" >
        Id, ProductLineCode, LabCode, OrderNo, ParentOrderNo, ReportNo, ObjectNo, ExternalId,
    ExternalNo, ExternalObjectNo, SourceType, LanguageId, CompleteDate, BizVersionId, ActiveIndicator,
    CreatedBy, CreatedDate, ModifiedBy, ModifiedDate,LabId,ExcludeCustomerInterface
    </sql>

  <insert id="batchInsert" >
      INSERT INTO tb_test_data_object_rel (
        <include refid="Base_Column_List" />
    )
      VALUES
    <foreach collection="objectRels" item="objectRel" separator=",">
    (
        #{objectRel.id},
        #{objectRel.productLineCode},
        #{objectRel.labCode},
        #{objectRel.orderNo},
        #{objectRel.parentOrderNo},
        #{objectRel.reportNo},
        #{objectRel.objectNo},
        #{objectRel.externalId},
        #{objectRel.externalNo},
        #{objectRel.externalObjectNo},
        #{objectRel.sourceType},
        #{objectRel.languageId},
        #{objectRel.completeDate},
        #{objectRel.bizVersionId},
        #{objectRel.activeIndicator},
        #{objectRel.createdBy},
        #{objectRel.createdDate},
        #{objectRel.modifiedBy},
        #{objectRel.modifiedDate},
        #{objectRel.labId},
        #{objectRel.excludeCustomerInterface}
    )
    </foreach>
    ON DUPLICATE KEY UPDATE
        ProductLineCode = VALUES(productLineCode),
        LabCode = VALUES(labCode),
        OrderNo = VALUES(orderNo),
        ParentOrderNo = VALUES(parentOrderNo),
        ReportNo = VALUES(reportNo),
        ObjectNo = VALUES(objectNo),
        ExternalId = VALUES(externalId),
        ExternalNo = VALUES(externalNo),
        ExternalObjectNo = VALUES(externalObjectNo),
        SourceType = VALUES(sourceType),
        CompleteDate = VALUES(completeDate),
        ActiveIndicator = VALUES(activeIndicator),
        ModifiedDate = VALUES(modifiedDate),
        ModifiedBy = VALUES(modifiedBy),
        LabId = VALUES(labId),
      ExcludeCustomerInterface = VALUES(excludeCustomerInterface)
  </insert>

  <select id="getReportObjectRelByObjectNo" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO">
    select
        <include refid="Base_Column_List" />
    from tb_test_data_object_rel
    where ObjectNo = #{objectNo}
    and ActiveIndicator = 1
    order by createdDate
    limit 1
  </select>

  <select id="getReportObjectRelInfo" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO">
    SELECT
        <include refid="Base_Column_List" />
    FROM tb_test_data_object_rel
    WHERE ObjectNo = #{objectNo}
      <if test="reportNo != null and reportNo != ''">
          AND ReportNo = #{reportNo}
      </if>
      <if test="externalNo != null and externalNo != ''">
        AND ExternalNo = #{externalNo}
      </if>
      <if test="externalObjectNo != null and externalObjectNo != ''">
          AND ExternalObjectNo = #{externalObjectNo}
      </if>
      AND SourceType = #{sourceType}
    LIMIT 1
  </select>

  <update id="updateInvalidOrValid">
    update tb_test_data_object_rel
        set  ActiveIndicator = 0
        and modifiedBy = #{objectDto.modifiedBy}
        and modifiedDate = #{objectDto.modifiedDate}
      where ActiveIndicator = 1
        <if test="objectDto.objectNo!=null and objectDto.objectNo!=''">
          and ObjectNo = #{objectDto.objectNo}
        </if>
        <if test="objectDto.labCode!=null and objectDto.labCode!=''">
          and LabCode = #{objectDto.labCode}
        </if>
        <if test="objectDto.orderNo!=null and objectDto.orderNo!=''">
          and OrderNo = #{objectDto.orderNo}
        </if>
        <if test="objectDto.reportNo!=null and objectDto.reportNo!=''">
          and ReportNo = #{objectDto.reportNo}
        </if>
        <if test="objectDto.externalNo!=null and objectDto.externalNo!=''">
          and ExternalNo = #{objectDto.externalNo}
        </if>
        <if test="objectDto.externalObjectNo!=null and objectDto.externalObjectNo!=''">
          and ExternalObjectNo = #{objectDto.externalObjectNo}
        </if>
  </update>

  <select id="queryUploadTestDataBySubcontractNo" resultMap="uploadTestDataMap">
    select matrix.TestLineId,matrix.CitationName,report.AnalyteName,
    report.ReportUnit,matrix.SampleNo,report.TestValue,
    report.ReportLimit,matrix.EvaluationAlias,
    report.AnalyteSeq,matrix.SampleSeq,
    report.Languages
    from tb_test_data_object_rel rel
           inner join `tb_test_data_matrix_info_${suffix}` matrix on rel.id = matrix.ObjectRelId and matrix.ActiveIndicator = 1
           inner join `tb_test_data_info_${suffix}` report on rel.id = report.ObjectRelId and matrix.id = report.TestDataMatrixId  and report.ActiveIndicator=1
    <where>
        rel.ObjectNo= #{objectNo}
        <if test="externalObjectNo !=null and externalObjectNo!=''">
            and rel.ExternalObjectNo=#{externalObjectNo}
        </if>
    </where>
    and rel.ActiveIndicator=1
    order by report.AnalyteSeq
  </select>

  <update id="cancelUploadTestData">
    update
      tb_test_data_object_rel rel
      inner join `tb_test_data_matrix_info_${suffix}` matrix on rel.id = matrix.ObjectRelId
      inner join `tb_test_data_info_${suffix}` report on rel.id = report.ObjectRelId and matrix.TestMatrixId = report.TestMatrixId
      set rel.ActiveIndicator=0,matrix.ActiveIndicator=0,report.ActiveIndicator=0
    where rel.ObjectNo = #{objectNo} and rel.SourceType = 6
  </update>

  <select id="getReportObjectRelByorderNo" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO">
    select
    <include refid="Base_Column_List" />
    from tb_test_data_object_rel
    where OrderNo = #{orderNo}
    and ActiveIndicator = 1
    order by createdDate
    limit 1
  </select>
  <select id="getReportObjectRelByReportNoAndLabId" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO">
    select
    <include refid="Base_Column_List" />
    from tb_test_data_object_rel
    where ReportNo = #{reportNo} and LabId = #{labId}
    and ActiveIndicator = 1
    order by createdDate
    limit 1
  </select>

    <select id="getStarlimsObjectRelList" resultType="com.sgs.testdatabiz.facade.model.info.starlims.TestDataObjectRelInfo">
        SELECT
            <include refid="Base_Column_List" />
            ,LastModifiedTimestamp
        FROM tb_test_data_object_rel
        WHERE SourceType = 3
          AND ActiveIndicator = 1
        <if test="externalParentId != null and externalParentId != ''">
            AND OrderNo = #{externalParentId}
        </if>
        <if test="externalId != null and externalId != ''">
            AND ObjectNo = #{externalId}
        </if>
        <if test="folderNo != null and folderNo != ''">
            AND ExternalNo = #{folderNo}
        </if>
        <if test="lastModifiedTime != null and lastModifiedTime != ''">
            AND LastModifiedTimestamp = #{lastModifiedTime}
        </if>
        ORDER BY LastModifiedTimestamp DESC
        LIMIT 1000;
    </select>

    <select id="getAbnormalStarlimsData" resultType="com.sgs.testdatabiz.facade.model.info.starlims.StarLimsRelData">
        SELECT
            tr.OrderNo,tr.ExternalNo,tr.ObjectNo,tm.id as testMatrixId,tm.ExternalId,tm.Condition
        FROM `tb_test_data_matrix_info_${suffix}` tm
                 INNER JOIN tb_test_data_object_rel tr on tm.ObjectRelId = tr.id
        WHERE tm.CreatedBy='starlims'
          AND tm.ConclusionDisplay IS NOT NULL
          AND tm.ConclusionId IS NULL;
    </select>

  <select id="queryTestData" resultType="com.sgs.testdatabiz.facade.model.info.TestDataInfo" >
    SELECT DISTINCT
        tdor.OrderNo
        ,tdor.ObjectNo
        ,tdor.ReportNo
        ,tdor.ExternalNo
        ,tdor.SourceType AS sourceType
        ,tdor.LanguageId
        ,tdor.CompleteDate AS CompleteDate
        ,tdor.LabId
        ,tdor.ExcludeCustomerInterface
        ,tdmi.Id AS TestDataMatrixId
        ,tdmi.TestLineMappingId
        ,tdmi.ConclusionId AS MatrixConclusionId
        ,tdmi.ConclusionDisplay
        ,tdmi.ExternalCode
        ,tdmi.TestLineId
        ,tdmi.CitationId
        ,tdmi.CitationVersionId
        ,tdmi.SampleNo
        ,tdmi.ExternalSampleNo
        ,tdmi.ExtFields AS ExtFields
        ,tdmi.PpVersionId
        ,tdmi.TestLineSeq
        ,tdmi.condition
        ,tdi.ReportLimit
        ,tdi.AnalyteType
        ,tdi.AnalyteCode
        ,tdi.AnalyteName AS testAnalyteName
        ,tdi.ReportUnit
        ,tdi.AnalyteSeq
        ,tdi.TestValue
        ,tdi.Languages AS testDataLanguages
        ,tdi.parent_condition_name as testParentConditionName
        ,tdi.condition_name as testDataConditionName
        ,tdi.specimen_desc as testDataSpecimenDesc
        ,tdi.position as testDataPosition
        ,tdi.up_specimen_desc as testDataUpSpecimenDesc
        ,tdi.procedure_condition_name as testDataProcedureConditionName
        ,tdi.test_data_id as testDataId
    FROM tb_test_data_object_rel tdor
    INNER JOIN `tb_test_data_matrix_info_${labSuffix}` tdmi ON tdor.id = tdmi.ObjectRelId AND tdmi.ActiveIndicator = 1
    INNER JOIN `tb_test_data_info_${labSuffix}` tdi ON tdmi.id = tdi.TestDataMatrixId AND tdi.ActiveIndicator = 1
    WHERE tdor.ActiveIndicator = 1
      <if test="orderNo != null and orderNo != ''">
          and tdor.OrderNo = #{orderNo}
      </if>
      <if test="objectNos!=null and objectNos.size>0">
        and tdor.ObjectNo in
        <foreach collection="objectNos" item="objectNo" open="(" close=")" separator=",">
          #{objectNo}
        </foreach>
      </if>
      <if test="analyteType != null ">
          and tdi.AnalyteType = #{analyteType}
      </if>
      <if test="sampleNos!=null and sampleNos.size>0">
          AND tdmi.SampleNo in
          <foreach collection="sampleNos" item="sampleNo" separator="," open="(" close=")">
              #{sampleNo}
          </foreach>
      </if>
      <if test="sourceTypes != null and sourceTypes.size > 0 ">
          and tdor.SourceType in
          <foreach collection="sourceTypes" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
      </if>
      <if test="testLineIds != null and testLineIds.size > 0 ">
          and tdmi.TestLineId in
          <foreach collection="testLineIds" item="item" open="(" separator="," close=")">
              #{item}
          </foreach>
      </if>
      order by tdor.OrderNo, tdor.ObjectNo, tdor.ExternalNo, tdor.SourceType, tdmi.ExternalCode, tdi.AnalyteSeq
  </select>

    <select id="deleteByObjectNoAndExternalNo">
        delete tdor,tdmi, tdi from tb_test_data_object_rel tdor
            INNER JOIN `tb_test_data_matrix_info_${productLineCode}_${labCode}` tdmi ON tdor.id = tdmi.ObjectRelId
            INNER JOIN `tb_test_data_info_${productLineCode}_${labCode}` tdi ON tdor.id = tdi.ObjectRelId AND tdmi.id = tdi.TestDataMatrixId
        where tdor.ObjectNo=#{objectNo}
        <if test="externalNo != null and externalNo != ''">
            and tdor.ExternalNo = #{externalNo}
        </if>
        <if test="sourceType != null and sourceType != ''">
            and tdor.SourceType = #{sourceType}
        </if>
        <if test="externalObjectNo != null and externalObjectNo != ''">
            and tdor.ExternalObjectNo = #{externalObjectNo}
        </if>
    </select>

    <!-- 查询有效的分包数据 -->
    <select id="queryValidSubcontractData" resultType="com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO">
        SELECT
            tdor.Id,
            tdor.ProductLineCode,
            tdor.LabCode,
            tdor.OrderNo,
            tdor.ParentOrderNo,
            tdor.ReportNo,
            tdor.ObjectNo,
            tdor.ExternalId,
            tdor.ExternalNo,
            tdor.ExternalObjectNo,
            tdor.CompleteDate as completedDate,
            tdor.SourceType,
            tdor.LanguageId,
            tdor.ActiveIndicator,
            tdor.CreatedBy,
            tdor.CreatedDate,
            tdor.ModifiedBy,
            tdor.ModifiedDate
        FROM tb_test_data_object_rel tdor
        WHERE tdor.ActiveIndicator = 1
        <if test="queryDTO.orderNo != null and queryDTO.orderNo != ''">
           and tdor.OrderNo = #{queryDTO.orderNo}
        </if>
        <if test="queryDTO.objectNo != null and queryDTO.objectNo != ''">
          AND tdor.ObjectNo = #{queryDTO.objectNo}
        </if>
        <if test="queryDTO.labCode != null and queryDTO.labCode != ''">
            AND tdor.LabCode = #{queryDTO.labCode}
        </if>
        <if test="queryDTO.reportNo != null and queryDTO.reportNo != ''">
          AND tdor.ReportNo = #{queryDTO.reportNo}
        </if>
    </select>

</mapper>
