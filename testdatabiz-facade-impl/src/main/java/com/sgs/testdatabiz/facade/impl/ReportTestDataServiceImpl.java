package com.sgs.testdatabiz.facade.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.domain.service.testdata.TestDataHandler;
import com.sgs.testdatabiz.domain.service.testdata.factory.TestDataHandlerFactory;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.facade.v2.ReportTestDataService;
import org.springframework.stereotype.Service;

/**
 * @author: shawn.yang
 * @create: 2023-03-17 13:21
 */
@Service("reportTestDataServiceImpl")
public class ReportTestDataServiceImpl implements ReportTestDataService {

    private final TestDataHandlerFactory handlerFactory;



    @Override
    public BaseResponse<Void> importData(ReportTestDataInfo reportTestDataInfo) {
        TestDataHandler<Object> defaultHandler = handlerFactory.getDefaultHandler();
        return defaultHandler.importData(reportTestDataInfo);
    }

    public ReportTestDataServiceImpl(TestDataHandlerFactory handlerFactory) {
        this.handlerFactory = handlerFactory;
    }
}
