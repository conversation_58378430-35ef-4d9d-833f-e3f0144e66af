<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sgs.testdatabiz</groupId>
    <artifactId>sgs-testdatabiz</artifactId>
    <packaging>pom</packaging>
    <version>1.3.49</version>
    <modules>
        <module>testdatabiz-core</module>
        <module>testdatabiz-facade-model</module>
        <module>testdatabiz-dbstorages</module>
        <module>testdatabiz-domain</module>
        <module>testdatabiz-facade</module>
        <module>testdatabiz-facade-impl</module>
        <module>testdatabiz-integration</module>
        <module>testdatabiz-test</module>
        <module>testdatabiz-web</module>
        <module>testdatabiz-mybatis-generator</module>
<!--        <module>testdatabiz-sdk</module>-->
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <swagger.bootstrapui.version>1.9.6</swagger.bootstrapui.version>

        <!-- 文件拷贝时的编码 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <spring.boot.version>2.4.2</spring.boot.version>
        <redisson.version>3.13.6</redisson.version>

        <spring-kafka.version>2.4.0.RELEASE</spring-kafka.version>
        <kafkaclient.version>1.2.5</kafkaclient.version>

        <!--数据库配置-->
        <mybatis.version>3.4.1</mybatis.version>
        <mybatis-spring.version>1.3.0</mybatis-spring.version>
        <mysql.version>5.1.31</mysql.version>
        <druid.version>1.1.14</druid.version>

        <pagehelper.version>5.1.8</pagehelper.version>

        <httpclient.version>4.5.2</httpclient.version>
        <httpcore.version>4.4.4</httpcore.version>

        <jedis.version>3.3.0</jedis.version>

        <validation-api.version>2.0.1.Final</validation-api.version>
        <hibernate-validator.version>6.0.15.Final</hibernate-validator.version>

        <commons-lang3.version>3.4</commons-lang3.version>
        <commons-io.version>2.4</commons-io.version>
        <commons-net.version>3.4</commons-net.version>
        <commons-collection4.version>4.4</commons-collection4.version>
        <lombok.version>1.16.18</lombok.version>

        <log4j-over-slf4j.version>1.7.25</log4j-over-slf4j.version>
        <fastjson.version>1.2.4</fastjson.version>
        <poi.version>3.17</poi.version>
        <jboss-jaxrs-api.version>1.0.0.Final</jboss-jaxrs-api.version>

        <dubbo.version>2.8.4</dubbo.version>
        <junit.version>4.12</junit.version>
        <jmockit.version>1.27</jmockit.version>

        <zkclient.version>0.1</zkclient.version>
        <javassist.version>3.12.1.GA</javassist.version>

        <commons-discovery.version>0.5</commons-discovery.version>

        <dom4j.version>1.6.1</dom4j.version>
        <joda-time.verson>2.10</joda-time.verson>
        <cxf.version>3.1.6</cxf.version>

        <springfox.version>3.0.0</springfox.version>
        <hystrix-version>1.5.9</hystrix-version>

        <aviator.version>5.2.4</aviator.version>

        <caffeine.version>2.9.0</caffeine.version>
        <grus-async.version>1.0.9</grus-async.version>
        <hutool.version>5.7.18</hutool.version>
        <lombok.version>1.16.18</lombok.version>

        <sgs-framework.version>1.0.727-beta</sgs-framework.version>
        <otsnotes.version>1.1.110</otsnotes.version>
        <preorder-facade.version>2.1.147</preorder-facade.version>
        <grus-core.version>1.1.0</grus-core.version>
        <bizlog.version>1.1.0</bizlog.version>
        <trimslocal.version>1.1.50</trimslocal.version>
        <customerbiz.version>1.0.10</customerbiz.version>
        <extsystem.facade.version>1.0.55</extsystem.facade.version>
        <!-- self version -->
        <testdatabiz.version>1.3.49</testdatabiz.version>
        <testdatabiz-facade.version>1.3.49</testdatabiz-facade.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <testdatabiz-sdk.version>1.3.38</testdatabiz-sdk.version>
        <tool-traceability.version>1.0.27-SNAPSHOT</tool-traceability.version>
        <sgs-framework.config>1.0.739-beta</sgs-framework.config>
        <jetcache.version>2.6.7</jetcache.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis</artifactId>
                <version>${jetcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-autoconfigure</artifactId>
                <version>${jetcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-anno</artifactId>
                <version>${jetcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-anno-api</artifactId>
                <version>${jetcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis-springdata</artifactId>
                <version>${jetcache.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-context</artifactId>
                <version>3.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-config</artifactId>
                <version>${sgs-framework.config}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs</groupId>
                <artifactId>tool-traceability</artifactId>
                <version>${tool-traceability.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-project</artifactId>
                <version>2.2.20</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.testdatabiz</groupId>
                <artifactId>testdatabiz-sdk</artifactId>
                <version>${testdatabiz-sdk.version}</version>
            </dependency>
            <!-- spring-boot配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jdbc</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-logging</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.lettuce</groupId>
                        <artifactId>lettuce-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-thymeleaf</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <scope>test</scope>
            </dependency>
            <!--kafka-->
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring-kafka.version}</version>
            </dependency>
            <!--<dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka-test</artifactId>
                <scope>test</scope>
            </dependency>-->
            <dependency>
                <groupId>com.sgs.grus</groupId>
                <artifactId>sgs-grus-kafkaclient</artifactId>
                <version>${kafkaclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.kafka</groupId>
                        <artifactId>spring-kafka</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sgs.grus</groupId>
                <artifactId>sgs-grus-bizlog</artifactId>
                <version>${bizlog.version}</version>
            </dependency>


            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.jmockit</groupId>
                <artifactId>jmockit</artifactId>
                <version>${jmockit.version}</version>
                <scope>test</scope>
            </dependency>

            <!--数据库配置-->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- 第三方Mybatis分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <!--validator-->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>
            <!--<dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>-->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>3.4.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.jboss.spec.javax.ws.rs</groupId>
                <artifactId>jboss-jaxrs-api_2.0_spec</artifactId>
                <version>${jboss-jaxrs-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.sgroschupf</groupId>
                <artifactId>zkclient</artifactId>
                <version>${zkclient.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${javassist.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collection4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${log4j-over-slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>

            <!-- MethodProvider -->
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-rt-frontend-jaxws</artifactId>
                <version>${cxf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-rt-transports-http</artifactId>
                <version>${cxf.version}</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${springfox.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>swagger-bootstrap-ui</artifactId>
                <version>${swagger.bootstrapui.version}</version>
            </dependency>
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-core</artifactId>
                <version>${hystrix-version}</version>
            </dependency>
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-metrics-event-stream</artifactId>
                <version>${hystrix-version}</version>
            </dependency>
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-javanica</artifactId>
                <version>${hystrix-version}</version>
            </dependency>
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-servo-metrics-publisher</artifactId>
                <version>${hystrix-version}</version>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.verson}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.grus</groupId>
                <artifactId>sgs-grus-async</artifactId>
                <version>${grus-async.version}</version>
            </dependency>

            <!-- self -->
            <dependency>
                <groupId>com.sgs.testdatabiz</groupId>
                <artifactId>testdatabiz-domain</artifactId>
                <version>${testdatabiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.testdatabiz</groupId>
                <artifactId>testdatabiz-web</artifactId>
                <version>${testdatabiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.testdatabiz</groupId>
                <artifactId>testdatabiz-facade</artifactId>
                <version>${testdatabiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.testdatabiz</groupId>
                <artifactId>testdatabiz-facade-model</artifactId>
                <version>${testdatabiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.testdatabiz</groupId>
                <artifactId>testdatabiz-facade-impl</artifactId>
                <version>${testdatabiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.testdatabiz</groupId>
                <artifactId>testdatabiz-core</artifactId>
                <version>${testdatabiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.testdatabiz</groupId>
                <artifactId>testdatabiz-integration</artifactId>
                <version>${testdatabiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.testdatabiz</groupId>
                <artifactId>testdatabiz-test</artifactId>
                <version>${testdatabiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.testdatabiz</groupId>
                <artifactId>testdatabiz-dbstorages</artifactId>
                <version>${testdatabiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.testdatabiz</groupId>
                <artifactId>testdatabiz-mybatis-generator</artifactId>
                <version>${testdatabiz.version}</version>
            </dependency>

            <!--  -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
            </dependency>


            <dependency>
                <groupId>commons-discovery</groupId>
                <artifactId>commons-discovery</artifactId>
                <version>${commons-discovery.version}</version>
            </dependency>


            <dependency>
                <groupId>commons-discovery</groupId>
                <artifactId>commons-discovery</artifactId>
                <version>${commons-discovery.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons-net.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-core</artifactId>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-model</artifactId>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.framework</groupId>
                <artifactId>sgs-framework-tool</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>com.sgs.framework</groupId>
                        <artifactId>sgs-framework-swagger</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sgs.framework</groupId>
                        <artifactId>sgs-framework-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-web</artifactId>
                    </exclusion>
                </exclusions>
                <version>${sgs-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.preorder</groupId>
                <artifactId>preorder-facade</artifactId>
                <version>${preorder-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.otsnotes</groupId>
                <artifactId>otsnotes-facade</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sgs.testdatabiz</groupId>
                        <artifactId>testdatabiz-facade</artifactId>
                    </exclusion>
                </exclusions>
                <version>${otsnotes.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.otsnotes</groupId>
                <artifactId>otsnotes-facade-model</artifactId>
                <version>${otsnotes.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.grus</groupId>
                <artifactId>sgs-grus-core</artifactId>
                <version>${grus-core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sgs.trimslocal</groupId>
                <artifactId>trimslocal-facade</artifactId>
                <version>${trimslocal.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sgs.customerbiz</groupId>
                <artifactId>customerbiz-facade</artifactId>
                <version>${customerbiz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sgs.extsystem</groupId>
                <artifactId>extsystem-facade</artifactId>
                <version>${extsystem.facade.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>my-deploy-release</id>
            <url>https://cnmaven.sgs.net/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>my-deploy-snapshot</id>
            <url>https://cnmaven.sgs.net/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <basedir/>
                    <buildDirectory/>
                    <mainOutputDirectory/>
                    <outputDirectory/>
                    <projectArtifact/>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <outputDirectory/>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
