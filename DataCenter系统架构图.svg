<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1200" height="800" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
    DataCenter数据中台系统架构图
  </text>
  
  <!-- 业务系统区域 -->
  <rect x="50" y="80" width="280" height="300" rx="10" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
  <text x="190" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1976d2">
    业务系统层
  </text>
  
  <!-- Smart系统 -->
  <rect x="80" y="120" width="100" height="50" rx="5" fill="#42a5f5" stroke="#1976d2"/>
  <text x="130" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Smart系统</text>
  
  <!-- SCI系统 -->
  <rect x="200" y="120" width="100" height="50" rx="5" fill="#42a5f5" stroke="#1976d2"/>
  <text x="250" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">SCI系统</text>
  
  <!-- SODA系统 -->
  <rect x="80" y="190" width="100" height="50" rx="5" fill="#42a5f5" stroke="#1976d2"/>
  <text x="130" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">SODA系统</text>
  
  <!-- GPO系统 -->
  <rect x="200" y="190" width="100" height="50" rx="5" fill="#42a5f5" stroke="#1976d2"/>
  <text x="250" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">GPO系统</text>
  
  <!-- 业务数据库 -->
  <rect x="120" y="270" width="120" height="40" rx="5" fill="#2196f3" stroke="#1976d2"/>
  <text x="180" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">业务数据库</text>
  
  <!-- 数据同步层 -->
  <rect x="380" y="80" width="280" height="300" rx="10" fill="#fff3e0" stroke="#f57c00" stroke-width="2"/>
  <text x="520" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#f57c00">
    数据同步层
  </text>
  
  <!-- Binlog采集 -->
  <rect x="410" y="130" width="100" height="40" rx="5" fill="#ff9800" stroke="#f57c00"/>
  <text x="460" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Binlog采集</text>
  
  <!-- CDC工具 -->
  <rect x="530" y="130" width="100" height="40" rx="5" fill="#ff9800" stroke="#f57c00"/>
  <text x="580" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">CDC工具</text>
  
  <!-- 数据管道 -->
  <rect x="410" y="190" width="220" height="40" rx="5" fill="#ffb74d" stroke="#f57c00"/>
  <text x="520" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">实时数据管道(Kafka/Pulsar)</text>
  
  <!-- 数据清洗ETL -->
  <rect x="410" y="250" width="220" height="40" rx="5" fill="#ffcc02" stroke="#f57c00"/>
  <text x="520" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">数据清洗&ETL处理</text>
  
  <!-- DataCenter核心区域 -->
  <rect x="720" y="80" width="430" height="500" rx="10" fill="#e8f5e8" stroke="#388e3c" stroke-width="2"/>
  <text x="935" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#388e3c">
    DataCenter数据中台
  </text>
  
  <!-- 存储层 -->
  <rect x="750" y="130" width="370" height="120" rx="5" fill="#c8e6c9" stroke="#388e3c"/>
  <text x="935" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2e7d32">
    分布式存储层
  </text>
  
  <!-- Doris -->
  <rect x="770" y="170" width="100" height="30" rx="3" fill="#4caf50" stroke="#2e7d32"/>
  <text x="820" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Apache Doris</text>
  
  <!-- ClickHouse -->
  <rect x="890" y="170" width="100" height="30" rx="3" fill="#4caf50" stroke="#2e7d32"/>
  <text x="940" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">ClickHouse</text>
  
  <!-- StarRocks -->
  <rect x="1010" y="170" width="100" height="30" rx="3" fill="#4caf50" stroke="#2e7d32"/>
  <text x="1060" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">StarRocks</text>
  
  <!-- 贴源数据区 -->
  <rect x="770" y="210" width="160" height="30" rx="3" fill="#66bb6a" stroke="#2e7d32"/>
  <text x="850" y="230" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">贴源数据表</text>
  
  <!-- 清洗数据区 -->
  <rect x="950" y="210" width="160" height="30" rx="3" fill="#66bb6a" stroke="#2e7d32"/>
  <text x="1030" y="230" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">清洗聚合数据表</text>
  
  <!-- 数据服务层 -->
  <rect x="750" y="280" width="370" height="120" rx="5" fill="#bbdefb" stroke="#1976d2"/>
  <text x="935" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1976d2">
    数据服务层
  </text>
  
  <!-- 查询引擎 -->
  <rect x="770" y="320" width="100" height="30" rx="3" fill="#2196f3" stroke="#1976d2"/>
  <text x="820" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">查询引擎</text>
  
  <!-- 缓存层 -->
  <rect x="890" y="320" width="100" height="30" rx="3" fill="#2196f3" stroke="#1976d2"/>
  <text x="940" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">缓存层(Redis)</text>
  
  <!-- 元数据管理 -->
  <rect x="1010" y="320" width="100" height="30" rx="3" fill="#2196f3" stroke="#1976d2"/>
  <text x="1060" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">元数据管理</text>
  
  <!-- API网关 -->
  <rect x="830" y="360" width="210" height="30" rx="3" fill="#42a5f5" stroke="#1976d2"/>
  <text x="935" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">API网关 & 统一接口</text>
  
  <!-- 业务能力层 -->
  <rect x="750" y="430" width="370" height="120" rx="5" fill="#f3e5f5" stroke="#7b1fa2"/>
  <text x="935" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#7b1fa2">
    业务能力层
  </text>
  
  <!-- Buyer Summary -->
  <rect x="770" y="470" width="100" height="35" rx="3" fill="#9c27b0" stroke="#7b1fa2"/>
  <text x="820" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Buyer Summary</text>
  <text x="820" y="498" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">查询服务</text>
  
  <!-- RDC -->
  <rect x="890" y="470" width="100" height="35" rx="3" fill="#9c27b0" stroke="#7b1fa2"/>
  <text x="940" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">RDC报告</text>
  <text x="940" y="498" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">数据中心</text>
  
  <!-- ODC -->
  <rect x="1010" y="470" width="100" height="35" rx="3" fill="#9c27b0" stroke="#7b1fa2"/>
  <text x="1060" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">ODC操作</text>
  <text x="1060" y="498" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">数据查询</text>
  
  <!-- 查询能力说明 -->
  <rect x="770" y="515" width="340" height="25" rx="3" fill="#e1bee7" stroke="#7b1fa2"/>
  <text x="940" y="532" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#4a148c">
    支持：模糊查询 | 明细查询 | 聚合分析 | 实时查询
  </text>
  
  <!-- 客户端接入区域 -->
  <rect x="50" y="620" width="1100" height="120" rx="10" fill="#fafafa" stroke="#616161" stroke-width="2"/>
  <text x="600" y="645" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#424242">
    客户端接入层
  </text>
  
  <!-- Web应用 -->
  <rect x="150" y="660" width="120" height="40" rx="5" fill="#757575" stroke="#424242"/>
  <text x="210" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Web应用</text>
  
  <!-- 移动端 -->
  <rect x="300" y="660" width="120" height="40" rx="5" fill="#757575" stroke="#424242"/>
  <text x="360" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">移动端应用</text>
  
  <!-- 第三方系统 -->
  <rect x="450" y="660" width="120" height="40" rx="5" fill="#757575" stroke="#424242"/>
  <text x="510" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">第三方系统</text>
  
  <!-- BI工具 -->
  <rect x="600" y="660" width="120" height="40" rx="5" fill="#757575" stroke="#424242"/>
  <text x="660" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">BI分析工具</text>
  
  <!-- 数据分析师 -->
  <rect x="750" y="660" width="120" height="40" rx="5" fill="#757575" stroke="#424242"/>
  <text x="810" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">数据分析师</text>
  
  <!-- 开发者工具 -->
  <rect x="900" y="660" width="120" height="40" rx="5" fill="#757575" stroke="#424242"/>
  <text x="960" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">开发者工具</text>
  
  <!-- 数据流箭头 -->
  <!-- 业务系统到数据同步 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
    </marker>
  </defs>
  
  <!-- 业务系统到同步层 -->
  <line x1="330" y1="185" x2="380" y2="185" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="355" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">实时同步</text>
  
  <!-- 同步层到DataCenter -->
  <line x1="660" y1="185" x2="720" y2="185" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="690" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">数据入湖</text>
  
  <!-- DataCenter到客户端 -->
  <line x1="935" y1="550" x2="935" y2="620" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="970" y="585" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">API调用</text>
  
  <!-- 业务系统到客户端（查询） -->
  <line x1="190" y1="380" x2="190" y2="620" stroke="#ff5722" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  <text x="120" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#ff5722" transform="rotate(-90, 120, 500)">只读查询</text>
  
  <!-- 特性标注 -->
  <rect x="50" y="760" width="1100" height="30" rx="5" fill="#e8eaf6" stroke="#3f51b5"/>
  <text x="600" y="780" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#3f51b5">
    ✓ 实时数据同步  ✓ 读写分离  ✓ 分布式存储  ✓ 弹性扩展  ✓ 统一数据服务  ✓ 多维度查询分析
  </text>
</svg>