<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="businessGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="syncGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e1bee7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="datacenterGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="storageGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffcc02;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="serviceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fce4ec;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8bbd9;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <defs>
      <marker id="arrowhead" markerWidth="10" markerHeight="7" 
              refX="9" refY="3.5" orient="auto">
        <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
      </marker>
      <marker id="dashedArrow" markerWidth="10" markerHeight="7" 
              refX="9" refY="3.5" orient="auto">
        <polygon points="0 0, 10 3.5, 0 7" fill="#999" />
      </marker>
    </defs>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="800" fill="#f8f9fa" stroke="none"/>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#333">
    业务系统与DataCenter系统架构图
  </text>
  
  <!-- 业务应用系统层 -->
  <g id="business-systems">
    <rect x="50" y="80" width="280" height="120" rx="10" fill="url(#businessGradient)" stroke="#1976d2" stroke-width="2"/>
    <text x="190" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1976d2">业务应用系统</text>
    
    <!-- 各个业务系统 -->
    <rect x="70" y="120" width="60" height="30" rx="5" fill="#2196f3" stroke="#1565c0" stroke-width="1"/>
    <text x="100" y="138" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Smart</text>
    
    <rect x="140" y="120" width="60" height="30" rx="5" fill="#2196f3" stroke="#1565c0" stroke-width="1"/>
    <text x="170" y="138" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">SCI</text>
    
    <rect x="210" y="120" width="60" height="30" rx="5" fill="#2196f3" stroke="#1565c0" stroke-width="1"/>
    <text x="240" y="138" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">SODA</text>
    
    <rect x="280" y="120" width="60" height="30" rx="5" fill="#2196f3" stroke="#1565c0" stroke-width="1"/>
    <text x="310" y="138" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">GPO</text>
  </g>
  
  <!-- 数据同步层 -->
  <g id="sync-layer">
    <rect x="50" y="240" width="280" height="80" rx="10" fill="url(#syncGradient)" stroke="#7b1fa2" stroke-width="2"/>
    <text x="190" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#7b1fa2">数据同步层</text>
    
    <rect x="70" y="280" width="80" height="25" rx="5" fill="#9c27b0" stroke="#6a1b9a" stroke-width="1"/>
    <text x="110" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Binlog采集</text>
    
    <rect x="160" y="280" width="80" height="25" rx="5" fill="#9c27b0" stroke="#6a1b9a" stroke-width="1"/>
    <text x="200" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">CDC工具</text>
    
    <rect x="250" y="280" width="80" height="25" rx="5" fill="#9c27b0" stroke="#6a1b9a" stroke-width="1"/>
    <text x="290" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">数据同步服务</text>
  </g>
  
  <!-- DataCenter 数据中心 -->
  <g id="datacenter">
    <rect x="400" y="80" width="750" height="500" rx="15" fill="url(#datacenterGradient)" stroke="#388e3c" stroke-width="3"/>
    <text x="775" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2e7d32">DataCenter 数据中心</text>
    
    <!-- 数据存储层 -->
    <g id="storage-layer">
      <rect x="420" y="130" width="320" height="100" rx="8" fill="url(#storageGradient)" stroke="#f57c00" stroke-width="2"/>
      <text x="580" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#e65100">数据存储层</text>
      
      <ellipse cx="500" cy="190" rx="60" ry="25" fill="#ff9800" stroke="#f57c00" stroke-width="2"/>
      <text x="500" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Apache Doris</text>
      <text x="500" y="198" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">分布式列式数据库</text>
      
      <ellipse cx="640" cy="190" rx="60" ry="25" fill="#ff9800" stroke="#f57c00" stroke-width="2"/>
      <text x="640" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">其他分布式</text>
      <text x="640" y="198" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">数据库</text>
    </g>
    
    <!-- 数据处理层 -->
    <g id="processing-layer">
      <rect x="420" y="260" width="320" height="80" rx="8" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
      <text x="580" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">数据处理层</text>
      
      <rect x="440" y="305" width="80" height="25" rx="5" fill="#66bb6a" stroke="#4caf50" stroke-width="1"/>
      <text x="480" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">数据清洗ETL</text>
      
      <rect x="530" y="305" width="80" height="25" rx="5" fill="#66bb6a" stroke="#4caf50" stroke-width="1"/>
      <text x="570" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">数据合并处理</text>
      
      <rect x="620" y="305" width="80" height="25" rx="5" fill="#66bb6a" stroke="#4caf50" stroke-width="1"/>
      <text x="660" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">数据建模</text>
    </g>
    
    <!-- 数据服务层 -->
    <g id="service-layer">
      <rect x="420" y="370" width="320" height="120" rx="8" fill="#81c784" stroke="#388e3c" stroke-width="2"/>
      <text x="580" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">数据服务层</text>
      
      <rect x="430" y="410" width="90" height="25" rx="5" fill="#a5d6a7" stroke="#66bb6a" stroke-width="1"/>
      <text x="475" y="425" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#2e7d32">贴源表查询API</text>
      
      <rect x="530" y="410" width="90" height="25" rx="5" fill="#a5d6a7" stroke="#66bb6a" stroke-width="1"/>
      <text x="575" y="425" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#2e7d32">聚合数据查询API</text>
      
      <rect x="630" y="410" width="90" height="25" rx="5" fill="#a5d6a7" stroke="#66bb6a" stroke-width="1"/>
      <text x="675" y="425" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#2e7d32">模糊查询API</text>
      
      <rect x="480" y="445" width="90" height="25" rx="5" fill="#a5d6a7" stroke="#66bb6a" stroke-width="1"/>
      <text x="525" y="460" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#2e7d32">明细查询API</text>
      
      <rect x="580" y="445" width="90" height="25" rx="5" fill="#a5d6a7" stroke="#66bb6a" stroke-width="1"/>
      <text x="625" y="460" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#2e7d32">聚合分析API</text>
    </g>
  </g>
  
  <!-- 对外查询服务 -->
  <g id="external-services">
    <rect x="800" y="620" width="350" height="120" rx="10" fill="url(#serviceGradient)" stroke="#c2185b" stroke-width="2"/>
    <text x="975" y="645" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#ad1457">对外查询服务</text>
    
    <rect x="820" y="665" width="100" height="40" rx="5" fill="#e91e63" stroke="#c2185b" stroke-width="1"/>
    <text x="870" y="680" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Buyer Summary</text>
    <text x="870" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">买家汇总查询</text>
    
    <rect x="940" y="665" width="100" height="40" rx="5" fill="#e91e63" stroke="#c2185b" stroke-width="1"/>
    <text x="990" y="680" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">RDC</text>
    <text x="990" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">报表数据中心</text>
    
    <rect x="1060" y="665" width="100" height="40" rx="5" fill="#e91e63" stroke="#c2185b" stroke-width="1"/>
    <text x="1110" y="680" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">ODC</text>
    <text x="1110" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">操作数据查询</text>
  </g>
  
  <!-- 数据流向箭头 -->
  <!-- 业务系统到同步层 -->
  <line x1="190" y1="200" x2="190" y2="240" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 同步层到数据中心 -->
  <line x1="330" y1="280" x2="400" y2="280" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="280" x2="500" y2="230" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="280" x2="640" y2="230" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 存储层到处理层 -->
  <line x1="570" y1="230" x2="570" y2="260" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 处理层到服务层 -->
  <line x1="580" y1="340" x2="580" y2="370" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 服务层到对外服务 -->
  <line x1="580" y1="490" x2="580" y2="550" stroke="#666" stroke-width="2"/>
  <line x1="580" y1="550" x2="870" y2="620" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="580" y1="550" x2="990" y2="620" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="580" y1="550" x2="1110" y2="620" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 业务系统API调用（虚线） -->
  <line x1="100" y1="200" x2="100" y2="750" stroke="#999" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="100" y1="750" x2="870" y2="750" stroke="#999" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#dashedArrow)"/>
  
  <line x1="170" y1="200" x2="170" y2="760" stroke="#999" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="170" y1="760" x2="990" y2="760" stroke="#999" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#dashedArrow)"/>
  
  <line x1="240" y1="200" x2="240" y2="770" stroke="#999" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="240" y1="770" x2="1110" y2="770" stroke="#999" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#dashedArrow)"/>
  
  <!-- 图例 -->
  <g id="legend">
    <rect x="50" y="720" width="200" height="60" rx="5" fill="white" stroke="#ccc" stroke-width="1"/>
    <text x="60" y="735" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">图例</text>
    
    <line x1="60" y1="745" x2="90" y2="745" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
    <text x="95" y="750" font-family="Arial, sans-serif" font-size="10" fill="#333">数据流向</text>
    
    <line x1="60" y1="760" x2="90" y2="760" stroke="#999" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#dashedArrow)"/>
    <text x="95" y="765" font-family="Arial, sans-serif" font-size="10" fill="#333">API调用</text>
  </g>
</svg>
