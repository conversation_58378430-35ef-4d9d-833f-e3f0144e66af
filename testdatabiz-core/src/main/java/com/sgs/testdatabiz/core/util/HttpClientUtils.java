/*
 *
 * (C) Copyright 2016 Ymatou (http://www.ymatou.com/).
 * All rights reserved.
 *
 */

package com.sgs.testdatabiz.core.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.sgs.tools.traceability.interceptor.HttpClientHeaderInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.core.MediaType;
import java.io.*;
import java.net.*;
import java.util.*;

public class HttpClientUtils {
    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtils.class);

    // 超时时间豪秒
    private static final Integer CONN_TIME_OUT = 3000;
    //Socket 超时时间
    private static final Integer SOCKET_TIME_OUT = 10000;
    private static final Integer DEFAULT_MAX_PER_ROUTE = 200;
    private static final Integer MAX_TOTAL = 1000;
    private static final RequestConfig requestConfig;
    private static final HttpClient httpClient;
    // 编码格式。发送编码格式统一用UTF-8
    private static String ENCODING = "UTF-8";
    //private static Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);

    static {
        requestConfig = RequestConfig.custom().setConnectionRequestTimeout(CONN_TIME_OUT)
                .setSocketTimeout(SOCKET_TIME_OUT).build();

        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        cm.setDefaultMaxPerRoute(DEFAULT_MAX_PER_ROUTE);//每个route默认的最大连接数
        cm.setMaxTotal(MAX_TOTAL);//整个连接池的最大连接数

        httpClient = HttpClients.custom().setConnectionManager(cm).setDefaultRequestConfig(requestConfig).addInterceptorFirst(new HttpClientHeaderInterceptor()).build();
    }

    /**
     * 基于HttpClient 4.5的通用POST方法
     *
     * @param url
     *            提交的URL
     * @param paramMaps
     *            提交<参数，值>Map
     * @return 提交响应
     */
    public static <T> T post(String url, Map<String, String> paramMaps, TypeReference<T> typeRefs) {
        try {
            String jsonStr = post(url, paramMaps);
            if (StringUtils.isEmpty(jsonStr)){
                return null;
            }
            return JSON.parseObject(jsonStr, typeRefs);
        } catch (Exception ex) {
            logger.info("post fail,url:"+url+",Exception"+ex.getMessage());
        }
        return null;
    }

    /**
     *
     * @param url
     * @param paramMaps
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T post(String url, Map<String, String> paramMaps, Class<T> clazz) {
        try {
            String jsonStr = post(url, paramMaps);
            if (StringUtils.isEmpty(jsonStr)){
                return null;
            }
            return JSON.parseObject(jsonStr, clazz);
        } catch (Exception ex) {
            logger.info("post fail,url:"+url+",Exception"+ex.getMessage());
        }
        return null;
    }

    public static String post(String url, Map<String, String> paramMaps) {
        CloseableHttpClient client = HttpClients.createDefault();
        String jsonStr = "";
        CloseableHttpResponse response = null;
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(requestConfig);
            if (paramMaps != null) {
                List<NameValuePair> paramList = Lists.newArrayList();
                for (Map.Entry<String, String> param : paramMaps.entrySet()) {
                    NameValuePair pair = new BasicNameValuePair(param.getKey(), param.getValue());
                    paramList.add(pair);
                }
                httpPost.setEntity(new UrlEncodedFormEntity(paramList, ENCODING));
            }
            response = client.execute(httpPost);

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                jsonStr = EntityUtils.toString(entity);
            }
            if (StringUtils.isEmpty(jsonStr)){
                return null;
            }
            return jsonStr;
        } catch (IOException e) {
            logger.info("post fail,url:"+url+",Exception"+e.getMessage());
            //将当前异常抛出，由业务系统处理异常
        } finally {
            //关闭response
            try {
                if(!Objects.isNull(response)){
                    response.close();
                }
                if(!Objects.isNull(client)){
                    client.close();
                }
            } catch (Exception e) {
                logger.info("post fail,url:"+url+",Exception"+e.getMessage());
            }
        }
        return null;
    }

    public static String doPostFrom(String url, Map<String, String> params) {
        URL u = null;
        HttpURLConnection con = null;
        // 构建请求参数
        StringBuffer sb = new StringBuffer();
        if (params != null) {
            for (Map.Entry<String, String> e : params.entrySet()) {
                sb.append(e.getKey());
                sb.append("=");
                sb.append(e.getValue());
                sb.append("&");
            }
            sb.substring(0, sb.length() - 1);
        }
        System.out.println("send_url:" + url);
        System.out.println("send_data:" + sb.toString());
        // 尝试发送请求
        try {
            u = new URL(url);
            con = (HttpURLConnection) u.openConnection();
            con.setRequestMethod("POST");
            con.setDoOutput(true);
            con.setDoInput(true);
            con.setUseCaches(false);
            con.setRequestProperty("Content-Type", MediaType.APPLICATION_FORM_URLENCODED);
            OutputStreamWriter osw = new OutputStreamWriter(con.getOutputStream(), "UTF-8");
            osw.write(sb.toString());
            osw.flush();
            osw.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (con != null) {
                con.disconnect();
            }
        }

        // 读取返回内容
        StringBuffer buffer = new StringBuffer();
        try {
            //一定要有返回值，否则无法把请求发送给server端。
            assert con != null;
            BufferedReader br = new BufferedReader(new InputStreamReader(con.getInputStream(), "UTF-8"));
            String temp;
            while ((temp = br.readLine()) != null) {
                buffer.append(temp);
                buffer.append("\n");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return buffer.toString();
    }

    public static String requestGet(String url, Map<String, String> paramsMap, Integer timeout) throws Exception {
        //logger.info("GET request  url:{} params:{}", url, paramsMap);

        //Long start = System.currentTimeMillis();

        List<NameValuePair> params = initParams(paramsMap);
        // Get请求
        HttpGet httpGet = new HttpGet(url);

        try {
            if (timeout != null && timeout > 0) {
                httpGet.setConfig(RequestConfig.copy(requestConfig).setSocketTimeout(timeout).build());
            }

            // 设置参数
            String str = EntityUtils.toString(new UrlEncodedFormEntity(params, Consts.UTF_8));
            httpGet.setURI(new URI(httpGet.getURI().toString() + "?" + str));
            // 发送请求
            HttpResponse response = httpClient.execute(httpGet);
            //logger.info("GET request url:{} params:{} response:{} time:{}", url, paramsMap, response, System.currentTimeMillis() - start);
            // 获取返回数据
            String retStr = getSuccessRetFromResp(response, url, JSON.toJSONString(paramsMap));

            return retStr;
        } finally {
            httpGet.releaseConnection();
        }
    }

    /**
     *
     * @param reqUrl
     * @param paramMaps
     * @param host
     * @param port
     * @return
     * @throws Exception
     */
    public static String sendGet(String reqUrl, Map<String, String> paramMaps, String host, int port) throws Exception {
        StringBuffer append = new StringBuffer();
        BufferedReader reader = null;
        try {
            List<NameValuePair> params = initParams(paramMaps);
            // 设置参数
            String reqParams = EntityUtils.toString(new UrlEncodedFormEntity(params, Consts.UTF_8));

            URL client = new URL(String.format("%s?%s", reqUrl, reqParams));
            // 设置代理
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port));
            URLConnection connection = client.openConnection(proxy);

            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"));

            String line;
            while ((line = reader.readLine()) != null) {
                append.append(line);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException e) {
            }
        }
        return append.toString();
    }

    public static String sendGet(String reqUrl, Map<String, String> paramMaps) throws Exception {
        return requestGet(reqUrl, paramMaps, null);
    }

    /**
     *
     * @param reqUrl
     * @param paramMaps
     * @param host
     * @param port
     * @return
     * @throws Exception
     */
    public static String sendPost(String reqUrl, Map<String, Object> paramMaps, String host, int port) throws Exception {
        StringBuffer append = new StringBuffer();
        BufferedReader reader = null;
        PrintWriter out = null;
        try {
            // 设置参数
            String reqParams = JSON.toJSONString(paramMaps);
            URL client = new URL(reqUrl);
            // 设置代理
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port));
            URLConnection connection = client.openConnection(proxy);

            connection.setRequestProperty("Content-Type", MediaType.APPLICATION_JSON);
            // 设置超时时间
            /*connection.setConnectTimeout(5000);
            connection.setReadTimeout(15000);*/

            // 发送POST请求必须设置如下两行
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(connection.getOutputStream());
            // 发送请求参数
            out.print(reqParams);
            // flush输出流的缓冲
            out.flush();

            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"));
            String line;
            while ((line = reader.readLine()) != null) {
                append.append(line);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (Exception  e) {
            }
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException e) {
            }
        }
        return append.toString();
    }

    /**
     *
     * @param reqUrl
     * @param paramMaps
     * @param typeRefs
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> T sendPost(String reqUrl, Map<String, Object> paramMaps, TypeReference<T> typeRefs) throws Exception {
        String jsonStr = requestPostJsonStr(reqUrl, JSON.toJSONString(paramMaps));
        if (StringUtils.isEmpty(jsonStr)){
            return null;
        }
        return JSON.parseObject(jsonStr, typeRefs);
    }

    public static <T> T sendPost(String reqUrl, Map<String, Object> paramMaps, Class<T> clazz) throws Exception {
        String jsonStr = requestPostJsonStr(reqUrl, JSON.toJSONString(paramMaps));
        if (StringUtils.isEmpty(jsonStr)){
            return null;
        }
        return JSON.parseObject(jsonStr, clazz);
    }

    /**
     *
     * @param reqUrl
     * @param reqParams
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> sendPost(String reqUrl, Object reqParams, Class<T> clazz) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(reqParams));
        if (StringUtils.isEmpty(jsonStr)){
            return null;
        }
        return JSON.parseArray(jsonStr, clazz);
    }

    /**
     *
     * @param reqUrl
     * @param reqParams
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> T post(String reqUrl, Object reqParams, Class<T> clazz) throws Exception {
        String jsonStr = postJson(reqUrl, JSON.toJSONString(reqParams));
        if (StringUtils.isEmpty(jsonStr)){
            return null;
        }
        return JSON.parseObject(jsonStr, clazz);
    }

    /**
     * post json 格式数据
     *
     * @param url
     * @param params
     * @return
     * @throws Exception
     */
    public static String postJson(String url, String params) throws Exception {
        return postJson(url, params, null);
    }

    /**
     * post json 格式数据
     *
     * @param url
     * @param params
     * @return
     * @throws Exception
     */
    public static String postJson(String url, String params, Integer timeout) throws Exception {

        //logger.info("POST request url:{} params:{}", url, params);

        //Long start = System.currentTimeMillis();

        HttpPost httpPost = new HttpPost(url);

        try {
            if (timeout != null && timeout > 0) {
                httpPost.setConfig(RequestConfig.copy(requestConfig).setSocketTimeout(timeout).build());
            }

            StringEntity entity = new StringEntity(params, Consts.UTF_8);
            entity.setContentType(MediaType.APPLICATION_JSON);

            httpPost.setEntity(entity);

            HttpResponse response = httpClient.execute(httpPost);

            //logger.info("POST request url:{} time:{}", url, System.currentTimeMillis() - start);

            String retStr = getSuccessRetFromResp(response, url, params);

            return retStr;
        } finally {
            httpPost.releaseConnection();
        }

    }

    /**
     * post json 格式数据
     *
     * @param url
     * @param params
     * @return
     * @throws Exception
     */
    public static String requestPostJsonStr(String url, String params) throws Exception {
        return requestPostJsonStr(url, params, null);
    }

    /**
     * post json 格式数据
     *
     * @param url
     * @param params
     * @return
     * @throws Exception
     */
    public static String requestPostJsonStr(String url, String params, Integer timeout) throws Exception {

        //logger.info("POST request url:{} params:{}", url, params);

        //Long start = System.currentTimeMillis();

        HttpPost httpPost = new HttpPost(url);

        try {
            if (timeout != null && timeout > 0) {
                httpPost.setConfig(RequestConfig.copy(requestConfig).setSocketTimeout(timeout).build());
            }

            StringEntity entity = new StringEntity(params, Consts.UTF_8);
            entity.setContentType(MediaType.APPLICATION_JSON);

            httpPost.setEntity(entity);

            HttpResponse response = httpClient.execute(httpPost);

            //logger.info("POST request url:{} time:{}", url, System.currentTimeMillis() - start);

            String retStr = getSuccessRetFromResp(response, url, params);

            return retStr;
        } finally {
            httpPost.releaseConnection();
        }

    }

    /**
     * post json 格式数据
     *
     * @param url
     * @param obj
     * @return
     * @throws Exception
     */
    public static String requestPostJson(String url, Object obj) throws Exception {
        String params = JSON.toJSONString(obj);
        return requestPostJsonStr(url, params);
    }

    private static String getSuccessRetFromResp(HttpResponse response, String url, String params) throws Exception {
        String retStr;
        // 检验状态码，如果成功接收数据
        int code = response.getStatusLine().getStatusCode();

        if (code == 200) {
            retStr = EntityUtils.toString(response.getEntity(), Consts.UTF_8);
        } else {
            throw new RuntimeException(String.format("Http request error:%s, url:%s, params:%s", response, url, params));
        }

        //logger.info("Http request url:{} params={} retStr:{}. ", url, params, retStr);
        return retStr;
    }

    private static List<NameValuePair> initParams(Map<String, String> paramsMap) {
        List<NameValuePair> params = new ArrayList<>();
        if (paramsMap == null)
            return params;
        Iterator<String> iterator = paramsMap.keySet().iterator();


        while (iterator.hasNext()) {
            String key = iterator.next();
            params.add(new BasicNameValuePair(key, paramsMap.get(key)));
        }
        return params;
    }


    private static List<NameValuePair> getParams(Map<String, Object> paramMaps) {

        List<NameValuePair> params = new ArrayList<>();
        if (paramMaps == null)
            return params;
        Iterator<String> iterator = paramMaps.keySet().iterator();


        while (iterator.hasNext()) {
            String key = iterator.next();
            Object value = paramMaps.get(key);
            params.add(new BasicNameValuePair(key, value == null ? "" : value.toString()));
        }
        return params;
    }
}

