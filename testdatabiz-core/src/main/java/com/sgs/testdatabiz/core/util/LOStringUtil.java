package com.sgs.testdatabiz.core.util;

import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class LOStringUtil {

    /**
     *
     * @param text
     * @return
     */
    public static String decode(String text){
        if (StringUtils.isBlank(text)) {
            return text;
        }

        Map<String,String> unitMapping = ImmutableMap.<String,String>builder()
                .put("&lt;","<")
                .put("&gt;",">")
                .put("&le;","≤")
                .put("&ge;","≥")
                .put("&amp;","&")
                .put("&quot;","'")
                .put("&nbsp;"," ")
                .build();
        Set<Map.Entry<String, String>> entries = unitMapping.entrySet();
        for(Map.Entry<String,String> entry : entries){
            String key = entry.getKey();
            String value = entry.getValue();
            text = text.replace(key,value);
        }
        return  text.trim();
    }
    public static String encode(String text) {
        String result;
        char[] chars = text.toCharArray();
        char[] chRes = new char[chars.length];
        for (int i = 0; i < chars.length; i++) {
            if ((chars[i] >= 'A' && chars[i] < 'Z') || (chars[i] >= 'a' && chars[i] < 'z')) {
                chRes[i] = (char) (chars[i] - 1);
            } else if (chars[i] == 'z') {
                chRes[i] = 'a';
            } else if (chars[i] == 'Z') {
                chRes[i] = 'A';
            } else {
                chRes[i] = chars[i];
            }
        }
        result = String.valueOf(chRes);
        return result;
    }

    public static String replaceSup(String htmlStr){
        //ᵃ ᵇ ᶜ ᵈ ᵉ ᵍ ʰ ⁱ ʲ ᵏ ˡ ᵐ ⁿ ᵒ ᵖ ʳ ˢ ᵗ ᵘ ᵛ ʷ ˣ ʸ ᙆ ᴬ
        Map<String,String> unitMapping = ImmutableMap.<String,String>builder()
                .put("0","°")
                .put("1","¹")
                .put("2","²")
                .put("3","³")
                .put("4","⁴")
                .put("5","⁵")
                .put("6","⁶")
                .put("7","⁷")
                .put("8","⁸")
                .put("9","⁹")
                .put("a","ᵃ")
                .put("b","ᵇ")
                .put("c","ᶜ")
                .put("d","ᵈ")
                .put("e","ᵉ")
                //.put("f","")
                .put("g","ᵍ")
                .put("h","ʰ")
                .put("i","ⁱ ")
                .put("j","ʲ")
                .put("k","ᵏ")
                .put("l","ˡ")
                .put("m","ᵐ")
                .put("n","ⁿ")
                .put("o","ᵒ")
                .put("p","ᵖ")
               // .put("q","")
                .put("r","ʳ")
                .put("s","ˢ")
                .put("t","ᵗ")
                .put("u","ᵘ")
                .put("v","ᵛ")
                .put("w","ʷ")
                .put("x","ˣ")
                .put("y","ʸ")
                .put("z","ᙆ")
                .build();
        Pattern p=Pattern.compile("\\<sup\\>\\w+\\<\\/sup\\>",Pattern.CASE_INSENSITIVE);
        Matcher m2=p.matcher(htmlStr);
        while(m2.find()) {
            String find = m2.group(0).toLowerCase();
            String content = find.substring(find.indexOf("<sup>")+5,find.indexOf("</sup>"));
            String[] cons = content.split("");
            String ths = StringUtils.EMPTY;
            for(String ss : cons){
                String th = unitMapping.getOrDefault(ss,ss);
                ths+=th;
            }
            htmlStr = htmlStr.replace(m2.group(0),ths);
        }
        return htmlStr;
    }

    public static String replaceSub(String htmlStr){
        Map<String,String> unitMapping = ImmutableMap.<String,String>builder()
                .put("0","₀")
                .put("1","₁")
                .put("2","₂")
                .put("3","₃")
                .put("4","₄")
                .put("5","₅")
                .put("6","₆")
                .put("7","₇")
                .put("8","₈")
                .put("9","₉")
                .put("a","ₐ")
                .put("e","ₑ")
                .put("h","ₕ")
                .put("i","ᵢ")
                .put("j","ⱼ")
                .put("k","ₖ")
                .put("l","ₗ")
                .put("m","ₘ")
                .put("n","ₙ")
                .put("o","ₒ")
                .put("p","ₚ")
                .put("r","ᵣ")
                .put("s","ₛ")
                .put("t","ₜ")
                .put("u","ᵤ")
                .put("v","ᵥ")
                .put("x","ₓ").build();
        Pattern p=Pattern.compile("\\<sub\\>\\w+\\<\\/sub\\>",Pattern.CASE_INSENSITIVE);
        Matcher m2=p.matcher(htmlStr);
        while(m2.find()) {
            String find = m2.group(0).toLowerCase();
            String content = find.substring(find.indexOf("<sub>")+5,find.indexOf("</sub>"));
            String[] cons = content.split("");
            String ths = StringUtils.EMPTY;
            for(String ss : cons){
                String th = unitMapping.getOrDefault(ss.toLowerCase(),ss);
                ths+=th;
            }
            htmlStr = htmlStr.replace(m2.group(0),ths);
        }
        return htmlStr;
    }


    public static String delHTMLTag(String htmlStr) {
        if (StringUtils.isBlank(htmlStr)) {
            return htmlStr;
        }
        //先对特殊字符进行替换
        Map<String,String> unitMapping = ImmutableMap.<String,String>builder()
                .put("&micro;","μ")
                .put("&mu;","μ")
                .put("&sup2;","²")
                .put("&sup3;","³")
                .put("&bull;","•")
                .put("&#39;","'")
                .put("&middot;","·")
                .put("&Omega;","Ω")
                .put("&nbsp;"," ")
                .put("&Delta;","Δ")
                .put("&delta;","δ")
                .put("&deg;", "°")
                .put("&plusmn;", "±")
               /* .put("<sup>0</sup>","°")
                .put("<Sup>0</Sup>","°")
                .put("<sup>2</sup>","²")
                .put("<Sup>2</Sup>","²")
                .put("<sup>3</sup>","³")
                .put("<Sup>3</Sup>","³")*/.build();


        Set<Map.Entry<String, String>> entries = unitMapping.entrySet();
        for(Map.Entry<String,String> entry : entries){
            String key = entry.getKey();
            String value = entry.getValue();
            htmlStr = htmlStr.replace(key,value);
        }
        htmlStr = replaceSup(htmlStr);
        htmlStr = replaceSub(htmlStr);
        //定义script的正则表达式
        String regEx_script = "<script[^>]*?>[\\s\\S]*?<\\/script>";
        //定义style的正则表达式
        String regEx_style = "<style[^>]*?>[\\s\\S]*?<\\/style>";
        //定义HTML标签的正则表达式
        String regEx_html = "<[^>]+>";
        //htmlStr = htmlStr.replace("<sup>", "$$$").replace("</sup>", "@@@").replace("<sub>", "###").replace("</sub>", "&&&");

        Pattern p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
        Matcher m_script = p_script.matcher(htmlStr);
        //过滤script标签
        htmlStr = m_script.replaceAll("");

        Pattern p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
        Matcher m_style = p_style.matcher(htmlStr);
        //过滤style标签
        htmlStr = m_style.replaceAll("");

        Pattern p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
        Matcher m_html = p_html.matcher(htmlStr);
        //过滤html标签
        htmlStr = m_html.replaceAll("");

        //htmlStr = htmlStr.replace("$$$", "<sup>").replace("@@@", "</sup>").replace("###", "<sub>").replace("&&&", "</sub>");

        Map<String,String> symbolMapping = ImmutableMap.<String,String>builder()
                .put("<","&lt;")
                .put(">","&gt;")
                .put("<=","&le;")
                .put("≤","&le;")
                .put(">=","&ge;")
                .put("≥","&ge;")
                .build();

        Set<Map.Entry<String, String>> entries2 = symbolMapping.entrySet();
        for(Map.Entry<String,String> entry : entries2){
            htmlStr = htmlStr.replace(entry.getKey(), entry.getValue());
        }
        //返回文本字符串
        return htmlStr.trim();
    }
}
