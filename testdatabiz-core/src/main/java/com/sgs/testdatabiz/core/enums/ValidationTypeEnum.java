package com.sgs.testdatabiz.core.enums;

public enum ValidationTypeEnum {

    CUSTOM("customer", "客户字段校验"),
    SUBCONTRACT_TABLE("subcontractTable", "分包表结构校验"),
    CONCLUSION_DATA("conclusion", "结论数据校验"),
    SYSTEM_ORDER_NO("systemOrderNO", "系统订单号校验"),
    TESTLINE_DATA("testLineData", "测试线数据校验"),
    LAB_CODE("labCode", "实验室代码校验"),
    REMOTE("remote", "远程校验"),
    SYSTEM_ID("systemId", "系统ID验证"),

    ;

    private String description;
    private String name;

    ValidationTypeEnum( String name,String description) {
        this.description = description;
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public String getDesc() {
        return description;
    }

}
