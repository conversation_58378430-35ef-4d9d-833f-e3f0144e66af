package com.sgs.testdatabiz.core.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

public final class HeaderHelper {
    private static final Logger logger = LoggerFactory.getLogger(HeaderHelper.class);
    /**
     *
     * @param paramName
     * @return
     */
    public static String getParamValue(String paramName) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        ServletRequestAttributes sra = (ServletRequestAttributes)requestAttributes;
        // DIG-8555  Change this condition so that it does not always evaluate to "false"
        /*if (sra == null) {
            return null;
        }*/
        HttpServletRequest request = sra.getRequest();
        if (request == null){
            return null;
        }
        String paramValue = null;
        try {
            paramValue = request.getParameter(paramName);
            if (StringUtils.isNotBlank(paramValue)) {
                return paramValue;
            }
            paramValue = request.getHeader(paramName);
            if (StringUtils.isNotBlank(paramValue)) {
                return paramValue;
            }
            paramValue = request.getHeader("X-Cookie");
            if (StringUtils.isNotBlank(paramValue)) {
                return paramValue;
            }
            Cookie[] cookies = request.getCookies();
            if (cookies == null || cookies.length <= 0){
                return paramValue;
            }
            for (Cookie cookie : cookies) {
                if (paramName.equals(cookie.getName())) {
                    paramValue = cookie.getValue();
                }
            }
        } catch (Exception ex) {
            logger.error("HeaderHelper.getParamValue：", ex);
        }
        return paramValue;
    }

}
