package com.sgs.testdatabiz.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 *
 */
@Configuration
public class CacheConfig {
    /**
     * 缓存类型
     */
    @Value("${guava.cacheType}")
    private String cacheType;

    /**
     * 缓存条目数
     */
    @Value("${guava.cacheSize}")
    private long cacheSize;

    /**
     * 缓存过期时间
     */
    @Value("${guava.expireTime}")
    private int expireTime;

    /**
     * 写缓存的线程数
     */
    @Value("${guava.writeConcurrencyNum}")
    private int writeConcurrencyNum;

    /**
     * 配置缓存数量上限，快达到上限或达到上限，处理了时间最长没被访问过的对象或者根据配置的被释放的对象
     */
    @Value("${guava.maximumSize}")
    private int maximumSize;
    /**
     * 缓存项在给定时间内没有被写访问（创建或覆盖），则回收
     */
    @Value("${guava.expireAfterWrite}")
    private int expireAfterWrite;
    /**
     * 设置并发级别为8，并发级别是指可以同时写缓存的线程数
     */
    @Value("${guava.concurrencyLevel}")
    private int concurrencyLevel;
    /**
     * 当设置的缓存过期时，则定时刷新
     */
    @Value("${guava.refreshAfterWrite}")
    private int refreshAfterWrite;
    /**
     * 设置缓存容器的初始容量，默认为16
     */
    @Value("${guava.initialCapacity}")
    private int initialCapacity;

    public String getCacheType() {
        return cacheType;
    }

    public void setCacheType(String cacheType) {
        this.cacheType = cacheType;
    }

    public long getCacheSize() {
        return cacheSize;
    }

    public void setCacheSize(long cacheSize) {
        this.cacheSize = cacheSize;
    }

    public int getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(int expireTime) {
        this.expireTime = expireTime;
    }

    public int getWriteConcurrencyNum() {
        return writeConcurrencyNum;
    }

    public void setWriteConcurrencyNum(int writeConcurrencyNum) {
        this.writeConcurrencyNum = writeConcurrencyNum;
    }

    public int getMaximumSize() {
        return maximumSize;
    }

    public void setMaximumSize(int maximumSize) {
        this.maximumSize = maximumSize;
    }

    public int getExpireAfterWrite() {
        return expireAfterWrite;
    }

    public void setExpireAfterWrite(int expireAfterWrite) {
        this.expireAfterWrite = expireAfterWrite;
    }

    public int getConcurrencyLevel() {
        return concurrencyLevel;
    }

    public void setConcurrencyLevel(int concurrencyLevel) {
        this.concurrencyLevel = concurrencyLevel;
    }

    public int getRefreshAfterWrite() {
        return refreshAfterWrite;
    }

    public void setRefreshAfterWrite(int refreshAfterWrite) {
        this.refreshAfterWrite = refreshAfterWrite;
    }

    public int getInitialCapacity() {
        return initialCapacity;
    }

    public void setInitialCapacity(int initialCapacity) {
        this.initialCapacity = initialCapacity;
    }
}