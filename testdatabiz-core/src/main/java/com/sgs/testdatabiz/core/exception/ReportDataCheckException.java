package com.sgs.testdatabiz.core.exception;

import com.sgs.framework.core.exception.StdBizException;
import com.sgs.testdatabiz.core.errorcode.ErrorCode;

public class ReportDataCheckException extends StdBizException {

    public ReportDataCheckException(String stdCode, String message) {
        super(stdCode, message);
    }

    public ReportDataCheckException(String stdCode, String message, Object[] args) {
        super(stdCode, message, args);
    }

    public ReportDataCheckException(String stdCode, String msg, Throwable cause) {
        super(stdCode, msg, cause);
    }
    public ReportDataCheckException(ErrorCode errorCode, Integer responseCode, String msg) {
        super(errorCode.getCode(),msg);
        this.setCode(responseCode);
    }
}
