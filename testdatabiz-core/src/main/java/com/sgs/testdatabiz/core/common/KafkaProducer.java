package com.sgs.testdatabiz.core.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
public class KafkaProducer {
    private final KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    public KafkaProducer(KafkaTemplate<String, String> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }


    /**
     * @param topic
     * @param key
     * @param msgBody
     */
    public void doSend(String topic, String key, Object msgBody) {
        kafkaTemplate.send(topic, key, JSON.toJSONString(msgBody, SerializerFeature.DisableCircularReferenceDetect));
    }

}
