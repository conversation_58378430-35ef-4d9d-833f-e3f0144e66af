package com.sgs.testdatabiz.core.enums;

public enum TestLineStatus {
    Typing(701, "Typing"),
	Submit(702, "Submitted"),
	Completed(703, "Completed"),
	SubContracted(704, "Subcontracted"),
	Entered(705, "Entered"),
	Cancelled(706, "Cancelled"),
	// Not Test、NC
	NC(707, "Not Test"),
	DR(708, "Document Review"),
	NA(709, "NA");
    
    private int status;
    private String message;
    
    TestLineStatus(int status, String message) {
        this.status = status;
        this.message = message;
    }
    
    public int getStatus() {
        return status;
    }
    
    public String getMessage() {
        return message;
    }
} 