package com.sgs.testdatabiz.core.enums;

public enum ConclusionDimType {
    None(0, "None"),
    MatrixDim(1, "Matrix"),
    SectionDim(2, "Section"),
    PpDim(3, "PP");

    private int code;
    private String message;

    ConclusionDimType(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
} 