package com.sgs.testdatabiz.core.errorcode.enums;

public enum ErrorBizModelEnum {

    TEST_DATA_BIZ("01","TEST_DATA_BIZ"),
    SUB_CONTACT_TEST_DATA_BIZ("02","SUB_CONTACT_TEST_DATA_BIZ"),
    REPORT_DATA_BIZ("03","REPORT_DATA_BIZ"),
    REPORT_DATA_VALIDATION("04","REPORT_DATA_VALIDATION"),;
    private final String code;
    private final String description;

    ErrorBizModelEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static ErrorBizModelEnum fromCode(String code) {
        for (ErrorBizModelEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
