package com.sgs.testdatabiz.core.annotation;

import com.sgs.framework.model.enums.EventTypeEnum;

import java.lang.annotation.*;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.TYPE, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface EventType {
    /**
     *
     * @return
     */
    EventTypeEnum eventType();

    /**
     *
     * @return
     */
    EventTypeEnum[] extend() default EventTypeEnum.None;
}
