package com.sgs.testdatabiz.core.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.Iterator;
import java.util.List;

public class JsonNodeUtils {


    /**
     * 指定 属性获取 结果
     * @param reqObject
     * @param itemName
     * @return
     */
    public static List<String> getJsonNodeList(JsonNode reqObject, String itemName) {
        List<String> resList = Lists.newArrayList();
        JsonNode getName = reqObject.get(itemName);
        if (getName == null) {
            return null;
        }
        if (getName.isArray()) {
            Iterator<JsonNode> it = getName.iterator();
            while (it.hasNext()) {
                JsonNode childNode = it.next();
                resList.add(childNode.asText());
            }
        } else {
            resList.add(getName.asText());
        }
        if (CollectionUtils.isEmpty(resList)) {
            return null;
        }
        return resList;
    }





}
