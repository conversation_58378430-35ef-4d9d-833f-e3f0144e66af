package com.sgs.testdatabiz.core.enums;

import com.sgs.framework.tool.utils.Func;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date create in 2022/10/26 0026 19:10
 */
public enum ReportEntryModeEnum {

    NA("NA", "无Matrix：手工上传PDF"),
    Host("Host", "自己维护及管理"),
    Light("Light", "各自维护，数据聚合"),
    Execute("Execute", "以执行方为准"),
    Merge("Merge", "SubReport Mode"),
    Ignore("Ignore", "SubReport Mode");

    static Map<String, ReportEntryModeEnum> maps = new HashMap<>();
    private String name;
    private String desc;

    ReportEntryModeEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 根据Key得到枚举的Value
     * Lambda表达式，比较判断（JDK 1.8）
     *
     * @param key
     * @return
     */
    public static ReportEntryModeEnum getEnumType(String key) {
        ReportEntryModeEnum[] alarmGrades = ReportEntryModeEnum.values();
        ReportEntryModeEnum result = Arrays.asList(alarmGrades).stream()
                .filter(alarmGrade -> alarmGrade.getDesc().equals(key))
                .findFirst().orElse(null);
        return result;
    }

    static {
        for (ReportEntryModeEnum type : ReportEntryModeEnum.values()) {
            maps.put(type.getName(), type);
        }
    }

    public static boolean check(String conclusionType) {
        if (Func.isBlank(conclusionType) || !maps.containsKey(conclusionType)) {
            return false;
        }
        return true;
    }
}
