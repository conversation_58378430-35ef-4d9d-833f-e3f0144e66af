package com.sgs.testdatabiz.core.errorcode.enums;

public enum ErrorFunctionTypeEnum {
    VALIDATION("01", "验证"),
    SAVE("02", "保存");
    private final String code;
    private final String description;

    ErrorFunctionTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    public static ErrorFunctionTypeEnum fromCode(String code) {
        for (ErrorFunctionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
