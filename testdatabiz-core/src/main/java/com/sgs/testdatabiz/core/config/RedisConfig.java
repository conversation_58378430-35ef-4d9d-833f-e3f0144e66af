package com.sgs.testdatabiz.core.config;

import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import redis.clients.jedis.JedisPoolConfig;

import java.util.Set;

@Configuration
public class RedisConfig {
    private Logger logger = LoggerFactory.getLogger(RedisConfig.class);

    /**
     * Cluster Nodes' address, pattern: ip1:port,ip2:port ...
     */
    @Value("${spring.redis.nodes}")
    private String clusterNodes;

    /**
     * Redis Node Set
     */
    private Set<RedisNode> clusterNodeSet;

    /**
     * Cluster Timeout
     */
    @Value("${spring.redis.timeout}")
    public int timeout = 10000;

    /**
     * Cluster Password
     */
    @Value("${spring.redis.password}")
    private String password;

    /**
     * Cluster max times to redirect
     */
    @Value("${spring.redis.max-redirects}")
    private Integer maxRedirects;

    /**
     * The minimum idle connections in pool, default 2
     */
    @Value("${spring.redis.pool.min-idle}")
    private int minIdle = 2;

    /**
     * The maximum idle connections in pool, default 10
     */
    @Value("${spring.redis.pool.max-idle}")
    private int maxIdle = 10;

    /**
     * Maximum active connections, -1 no limit
     */
    @Value("${spring.redis.pool.max-active}")
    public int maxTotal = 200;

    /**
     * Maximum time for waiting  available connection, -1 no limit
     */
    @Value("${spring.redis.pool.max-wait}")
    private long maxWait = 10000;

    /**
     * whether to validate the connection while borrow
     */
    @Value("${spring.redis.pool.testOnBorrow}")
    public boolean testOnBorrow = true;

    private long timeBetweenEvictionRunsMillis = 30000;
    private long minEvictableIdleTimeMillis = 30000;
    private int cacheTimeout = 36000;

    /**
     * Bean for default redis template
     * @return
     */
    @Bean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(getConnectionFactory());
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new JdkSerializationRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(new JdkSerializationRedisSerializer());
        return redisTemplate;
    }

    /**
     *
     * @return
     */
    public @Bean RedisConnectionFactory getConnectionFactory() {
        String[] nodes = clusterNodes.split(",");
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMaxTotal(maxTotal);
        poolConfig.setTestOnBorrow(testOnBorrow);
        poolConfig.setMinIdle(minIdle);
        poolConfig.setMaxWaitMillis(maxWait);
        poolConfig.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
        poolConfig.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);

        return new JedisConnectionFactory(new RedisClusterConfiguration(Lists.newArrayList(nodes)), poolConfig);
    }


    /*
    *//**
     * Bean for pool configuration
     * @return
     *//*
    @Bean(name = "jedisPoolConfig")
    public JedisPoolConfig jedisPoolConfig() {
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxIdle(maxIdle);
        config.setMaxTotal(maxTotal);
        config.setTestOnBorrow(testOnBorrow);
        config.setMinIdle(minIdle);
        config.setMaxWaitMillis(maxWait);
        config.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
        config.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
        return config;
    }

    @Bean
    public RedisCacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        return RedisCacheManager.create(connectionFactory);
    }


    *//**
     * Bean for cluster configuration
     * @return
     *//*
    @Bean(name = "redisClusterConfiguration")
    public RedisClusterConfiguration getClusterConfiguration() {
        splitClusterNodes();
        if (isClusterMode) {
            Map<String, Object> source = new HashMap<>();
            source.put("spring.redis.cluster.nodes", clusterNodes);
            source.put("spring.redis.cluster.timeout", timeout);
            source.put("spring.redis.cluster.max-redirects", maxRedirects);
            return new RedisClusterConfiguration(new MapPropertySource("RedisClusterConfiguration", source));
        }
        return null;
    }

    @Bean
    public RedisConnectionFactory jedisConnectionFactory() {
        RedisSentinelConfiguration sentinelConfig = new RedisSentinelConfiguration()
                .master("mymaster")
                .sentinel("127.0.0.1", 26379)
                .sentinel("127.0.0.1", 26380);
        return new JedisConnectionFactory(sentinelConfig);
    }

    *//**
     * Bean for connection factory
     * @return
     *//*
    @Bean(name = "jedisConnectionFactory")
    public JedisConnectionFactory getConnectionFactory() {
        JedisConnectionFactory factory = null;

        splitClusterNodes();
        *//** Cluster *//*
        if (isClusterMode) {
            factory = new JedisConnectionFactory(getClusterConfiguration(), jedisPoolConfig());
        }
        *//** Stand-alone *//*
        else if (clusterNodeSet.size() == 1) {
            factory = new JedisConnectionFactory(jedisPoolConfig());
            for (RedisNode redisNode : clusterNodeSet) {
                factory.setHostName(redisNode.getHost());
                factory.setPort(redisNode.getPort());
            }
        }
        if (factory != null) {
            factory.setTimeout(cacheTimeout);
            factory.setPassword(password);
        }
        *//*JedisShardInfo shardInfo = new JedisShardInfo("**************:6379");

        factory.setShardInfo(shardInfo);*//*

        return factory;
    }

    *//**
     * splits the cluster nodes config item
     *//*
    private void splitClusterNodes() {
        clusterNodeSet = new LinkedHashSet<>();
        if (StringUtils.hasText(clusterNodes)) {
            String[] nodes = clusterNodes.split(",");
            for (String node : nodes) {
                logger.debug("node = {}", node);
                String[] nodeConfig = node.split(":");
                if (nodeConfig.length > 1) {
                    RedisNode redisNode = new RedisNode(nodeConfig[0],
                            Integer.valueOf(nodeConfig[1]));
                    clusterNodeSet.add(redisNode);
                }
            }
        }
        if (clusterNodeSet.size() > 1) {
            isClusterMode = true;
        }
    }
        */
}
