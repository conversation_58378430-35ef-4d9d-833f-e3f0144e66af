package com.sgs.testdatabiz.core.cache;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.sgs.testdatabiz.core.config.CacheConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Guava缓存抽象
 * <AUTHOR>
 * @Description
 * @Date 2019/3/26 17:41
 */
public abstract class AbstractGuavaCache<K, V> {
    private static final Logger logger = LoggerFactory.getLogger(AbstractGuavaCache.class);
    /**
     * 数据刷新线程池
     */
    private ListeningExecutorService refreshPool = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(2));
    /**
     *
     */
    private LoadingCache<K, V> loadingCache;

    public AbstractGuavaCache(){
        this.loadingCache = null;
    }

    /**
     *
     * @param cacheConfig
     */
    public void loadCache(CacheConfig cacheConfig) {
        loadingCache = CacheBuilder.newBuilder()
                .maximumSize(cacheConfig.getMaximumSize())// 缓存大小
                // 设置写缓存后1分钟过期
                .expireAfterWrite(cacheConfig.getExpireAfterWrite(), TimeUnit.SECONDS)// 缓存时间
                // 设置并发级别为200，并发级别是指可以同时写缓存的线程数
                .concurrencyLevel(cacheConfig.getConcurrencyLevel()) // 写缓存的线程数
                .initialCapacity(cacheConfig.getInitialCapacity()) // 设置缓存容器的初始容量，默认为16
                .refreshAfterWrite(cacheConfig.getRefreshAfterWrite(), TimeUnit.MINUTES)
                // 设置要统计缓存的命中率，开启缓存统计功能
                .recordStats()
                .build(new CacheLoader<K, V>() { // build方法中可以指定CacheLoader，在缓存不存在时通过CacheLoader的实现自动加载缓存
                    @Override
                    public V load(K cacheKey){
                        logger.info("正在Load(Load_{})缓存数据({}).", cacheKey, cacheKey.hashCode());
                        return asyncCacheLoader(cacheKey, null);
                    }
                    @Override
                    public ListenableFuture<V> reload(K cacheKey, V oldValue){
                        return refreshPool.submit(()->{
                            logger.info("已提交(SubmitReload_{})缓存数据({}).", cacheKey, cacheKey.hashCode());
                            return asyncCacheLoader(cacheKey, oldValue);
                        });
                    }
                });
    }

    /**
     *
     * @param key
     * @return
     */
    protected abstract V asyncCacheLoader(K key, V oldValue);

    /**
     * 从cache中拿出数据操作
     * @param key
     * @return
     */
    public V getValue(K key){
        try {
            return loadingCache.get(key);
        } catch (Exception ex) {
            logger.error("从内存缓存中获取内容时发生异常，key: {}", key, ex);
        }
        return null;
    }

    /**
     *
     * @param key
     * @return
     */
    public boolean containsKey(K key){
        ConcurrentMap<K, V> kvMaps = loadingCache.asMap();
        if (kvMaps == null || kvMaps.isEmpty()){
            return false;
        }
        return kvMaps.containsKey(key);
    }

    /**
     *
     * @param key
     * @param value
     */
    public void put(K key, V value){
        if (this.containsKey(key)){
            return;
        }
        loadingCache.put(key, value);
    }

    /**
     *
     * @param key
     * @param value
     */
    public void put(K key, JsonNode value){
        if (this.containsKey(key)){
            return;
        }
        loadingCache.put(key, (V) value);
    }

    /**
     *
     * @param key
     * @param defaultValue
     * @return
     */
    public V getValueOrDef(K key, V defaultValue) {
        try {
            return loadingCache.get(key);
        } catch (Exception ex) {
            logger.error("从内存缓存中获取内容时发生异常，key: {}", key, ex);
            return defaultValue;
        }
    }
}
