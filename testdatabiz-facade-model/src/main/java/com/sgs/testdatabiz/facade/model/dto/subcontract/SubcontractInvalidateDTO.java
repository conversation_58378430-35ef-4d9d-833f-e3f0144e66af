package com.sgs.testdatabiz.facade.model.dto.subcontract;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 分包数据无效化DTO
 * 
 * 用于Mapper层方法参数封装，包含无效化操作所需的所有参数
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ToString
public class SubcontractInvalidateDTO {

    /**
     * 订单号
     */
    private String orderNo;


    /**
     * 实验室代码
     */
    private String labCode;

    /**
     * 报告号
     */
    private String reportNo;

    /**
     * 修改人
     */
    private String modifiedBy;

    /**
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * 默认构造函数
     */
    public SubcontractInvalidateDTO() {
    }

    /**
     * 全参构造函数
     * 
     * @param orderNo 订单号
     * @param labCode 实验室代码
     * @param reportNo 报告号
     * @param modifiedBy 修改人
     * @param modifiedDate 修改时间
     */
    public SubcontractInvalidateDTO(String orderNo,String labCode,
                                  String reportNo, String modifiedBy, Date modifiedDate) {
        this.orderNo = orderNo;
        this.labCode = labCode;
        this.reportNo = reportNo;
        this.modifiedBy = modifiedBy;
        this.modifiedDate = modifiedDate;
    }
}