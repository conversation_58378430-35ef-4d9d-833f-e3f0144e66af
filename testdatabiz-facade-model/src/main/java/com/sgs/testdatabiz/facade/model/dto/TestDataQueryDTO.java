package com.sgs.testdatabiz.facade.model.dto;

import com.sgs.framework.core.common.PrintFriendliness;

import java.util.List;

/**
 * @ClassName TestDataQueryDTO
 * @Description 查询DB DTO
 * <AUTHOR>
 * @Date 2022/6/10
 */
public class TestDataQueryDTO extends PrintFriendliness {
    private String orderNo;
    private List<String> objectNos;
    private String labCode;
    private List<Integer> testLineIdList;
    private List<Integer> standardIdList;
    private List<String> sampleNos;
    private Integer analyteType;
    private String labSuffix;
    private Boolean needSampleQuery;
    private List<Integer> sourceTypes;

    private List<Integer> testLineIds;

    public Boolean getNeedSampleQuery() {
        return needSampleQuery;
    }

    public void setNeedSampleQuery(Boolean needSampleQuery) {
        this.needSampleQuery = needSampleQuery;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<String> getObjectNos() {
        return objectNos;
    }

    public void setObjectNos(List<String> objectNos) {
        this.objectNos = objectNos;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public List<Integer> getTestLineIdList() {
        return testLineIdList;
    }

    public void setTestLineIdList(List<Integer> testLineIdList) {
        this.testLineIdList = testLineIdList;
    }

    public List<Integer> getStandardIdList() {
        return standardIdList;
    }

    public void setStandardIdList(List<Integer> standardIdList) {
        this.standardIdList = standardIdList;
    }

    public List<String> getSampleNos() {
        return sampleNos;
    }

    public void setSampleNos(List<String> sampleNos) {
        this.sampleNos = sampleNos;
    }

    public Integer getAnalyteType() {
        return analyteType;
    }

    public void setAnalyteType(Integer analyteType) {
        this.analyteType = analyteType;
    }

    public String getLabSuffix() {
        return labSuffix;
    }

    public void setLabSuffix(String labSuffix) {
        this.labSuffix = labSuffix;
    }

    public List<Integer> getSourceTypes() {
        return sourceTypes;
    }

    public void setSourceTypes(List<Integer> sourceTypes) {
        this.sourceTypes = sourceTypes;
    }

    public List<Integer> getTestLineIds() {
        return testLineIds;
    }

    public void setTestLineIds(List<Integer> testLineIds) {
        this.testLineIds = testLineIds;
    }
}
