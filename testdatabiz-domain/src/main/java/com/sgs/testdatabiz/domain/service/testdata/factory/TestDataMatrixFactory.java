package com.sgs.testdatabiz.domain.service.testdata.factory;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.enums.LanguageType;
import com.sgs.testdatabiz.core.util.DateUtils;
import com.sgs.testdatabiz.core.util.SnowflakeIdWorker;
import com.sgs.testdatabiz.core.util.Transcoding;
import com.sgs.testdatabiz.dbstorages.mybatis.enums.ActiveIndicatorEnum;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.domain.service.testdata.model.BizVersionId;
import com.sgs.testdatabiz.facade.model.info.TestDataMatrixExtFieldInfo;
import com.sgs.testdatabiz.facade.model.info.TestDataMatrixExtFieldLangInfo;
import com.sgs.testdatabiz.facade.model.info.starlims.TestDataMatrixLangInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataConditionInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataTestMatrixInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataTestMatrixLangInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 测试数据矩阵工厂类
 * 负责创建和构造TestDataMatrixInfoPO对象
 * 
 * <AUTHOR>
 */
@Component
public class TestDataMatrixFactory {

    @Autowired
    private SnowflakeIdWorker idWorker;

    /**
     * 从TestDataTestMatrixInfo创建TestDataMatrixInfoPO
     * 
     * @param objectRel  对象关系PO，提供关联信息和审计字段
     * @param matrixInfo 测试矩阵信息
     * @return 构造好的TestDataMatrixInfoPO对象
     * @throws IllegalArgumentException 如果matrixInfo为null
     */
    public TestDataMatrixInfoPO createTestMatrix(TestDataObjectRelPO objectRel, TestDataTestMatrixInfo matrixInfo) {
        if (matrixInfo == null) {
            throw new IllegalArgumentException("TestDataTestMatrixInfo cannot be null");
        }

        // 如果objectRel为null，创建一个默认的空对象
        if (objectRel == null) {
            objectRel = new TestDataObjectRelPO();
        }

        TestDataMatrixInfoPO testMatrix = new TestDataMatrixInfoPO();

        // 设置基本标识信息
        testMatrix.setId(idWorker.nextId());
        testMatrix.setObjectRelId(objectRel.getId());

        // 设置测试矩阵相关信息
        testMatrix.setTestMatrixId(matrixInfo.getTestMatrixId());
        testMatrix.setTestLineMappingId(matrixInfo.getTestLineMappingId());

        // 设置外部系统信息
        testMatrix.setExternalId(matrixInfo.getExternalId());
        testMatrix.setExternalCode(matrixInfo.getExternalCode());

        // 设置版本和引用信息
        testMatrix.setPpVersionId(matrixInfo.getPpVersionId());
        testMatrix.setAid(matrixInfo.getAid());
        testMatrix.setTestLineId(matrixInfo.getTestLineId());
        testMatrix.setCitationId(matrixInfo.getCitationId());
        testMatrix.setCitationVersionId(matrixInfo.getCitationVersionId());
        testMatrix.setCitationType(matrixInfo.getCitationType());
        testMatrix.setCitationName(matrixInfo.getCitationName());

        // 设置样本相关信息
        testMatrix.setSampleId(matrixInfo.getTestSampleId()); // externalId
        testMatrix.setTestLineInstanceId(matrixInfo.getTestLineInstanceId());
        testMatrix.setMatrixSource(matrixInfo.getMatrixSource());
        testMatrix.setSampleNo(matrixInfo.getTestSampleNo());
        testMatrix.setExternalSampleNo(matrixInfo.getExternalSampleNo());
        testMatrix.setTestLineSeq(matrixInfo.getTestLineSeq());
        testMatrix.setSampleSeq(matrixInfo.getSampleSeq());

        // 设置测试条件信息
        List<TestDataConditionInfo> testConditions = matrixInfo.getTestConditions();
        if (testConditions != null && !testConditions.isEmpty()) {
            testMatrix.setCondition(JSONObject.toJSONString(testConditions));
        }

        // 设置评估和结论信息
        testMatrix.setEvaluationAlias(matrixInfo.getEvaluationAlias());
        testMatrix.setMethodDesc(matrixInfo.getMethodDesc());
        testMatrix.setConclusionId(matrixInfo.getConclusionId());
        testMatrix.setConclusionDisplay(matrixInfo.getConclusionDisplay());

        // 设置扩展字段和多语言信息
        testMatrix.setExtFields(buildMatrixExtFieldInfo(matrixInfo));
        testMatrix.setLanguages(buildMatrixLangInfo(matrixInfo));

        // 设置业务版本ID和状态
        testMatrix.setBizVersionId(BizVersionId.fromMatrix(testMatrix).getValue());
        testMatrix.setActiveIndicator(
                (matrixInfo.isDisableMatrix() ? ActiveIndicatorEnum.DISABLE : ActiveIndicatorEnum.ACTIVE).getValue());

        // 设置审计信息
        testMatrix.setCreatedBy(objectRel.getCreatedBy());
        testMatrix.setCreatedDate(DateUtils.getNow());
        testMatrix.setModifiedBy(objectRel.getModifiedBy());
        testMatrix.setModifiedDate(DateUtils.getNow());

        return testMatrix;
    }

    /**
     * 更新测试矩阵的业务版本ID
     * 
     * @param testMatrix 需要更新的测试矩阵
     * @return 更新后的测试矩阵
     * @throws IllegalArgumentException 如果testMatrix为null
     */
    public TestDataMatrixInfoPO updateBizVersionId(TestDataMatrixInfoPO testMatrix) {
        if (testMatrix == null) {
            throw new IllegalArgumentException("TestDataMatrixInfoPO cannot be null");
        }

        testMatrix.setBizVersionId(BizVersionId.fromMatrix(testMatrix).getValue());
        return testMatrix;
    }

    /**
     * 为测试矩阵设置无效状态
     * 
     * @param testMatrix 需要设置为无效的测试矩阵
     * @return 设置后的测试矩阵
     * @throws IllegalArgumentException 如果testMatrix为null
     */
    public TestDataMatrixInfoPO markAsInactive(TestDataMatrixInfoPO testMatrix) {
        if (testMatrix == null) {
            throw new IllegalArgumentException("TestDataMatrixInfoPO cannot be null");
        }

        testMatrix.setActiveIndicator(ActiveIndicatorEnum.INACTIVE.getValue());
        return testMatrix;
    }

    /**
     * 构建矩阵扩展字段信息
     * 
     * @param matrixInfo 测试矩阵信息
     * @return JSON格式的扩展字段信息
     */
    private String buildMatrixExtFieldInfo(TestDataTestMatrixInfo matrixInfo) {
        TestDataMatrixExtFieldInfo extField = new TestDataMatrixExtFieldInfo();

        // 设置应用因子信息
        if (Func.isNotEmpty(matrixInfo.getAppFactorId())) {
            try {
                if (Integer.parseInt(matrixInfo.getAppFactorId().toString()) > 0) {
                    extField.setAppFactorId(matrixInfo.getAppFactorId());
                }
            } catch (NumberFormatException e) {
                // 忽略无效的数字格式
            }
        }
        extField.setAppFactorName(StringUtils.defaultIfBlank(matrixInfo.getAppFactorName(), null));

        // 设置材料相关信息
        extField.setMaterialName(StringUtils.defaultIfBlank(matrixInfo.getMaterialName(), null));
        extField.setMaterialColor(StringUtils.defaultIfBlank(matrixInfo.getMaterialColor(), null));
        extField.setMaterialTexture(StringUtils.defaultIfBlank(matrixInfo.getMaterialTexture(), null));
        extField.setUsedPosition(StringUtils.defaultIfBlank(matrixInfo.getUsedPosition(), null));

        // 设置引用信息 (SCI-1378)
        extField.setReferFromSampleNo(StringUtils.defaultIfBlank(matrixInfo.getReferFromSampleNo(), null));
        extField.setReferFromReportNo(matrixInfo.getReferFromReportNo());

        // 处理多语言扩展字段
        if (matrixInfo.getLanguages() == null) {
            matrixInfo.setLanguages(Lists.newLinkedList());
        }

        List<TestDataMatrixExtFieldLangInfo> languages = Lists.newArrayList();
        matrixInfo.getLanguages().forEach(lang -> {
            if (StringUtils.isBlank(lang.getMaterialName()) &&
                    StringUtils.isBlank(lang.getMaterialColor()) &&
                    StringUtils.isBlank(lang.getMaterialTexture())) {
                return;
            }

            TestDataMatrixExtFieldLangInfo language = new TestDataMatrixExtFieldLangInfo();
            BeanUtils.copyProperties(lang, language);
            language.setMaterialName(StringUtils.defaultIfBlank(lang.getMaterialName(), null));
            language.setMaterialColor(StringUtils.defaultIfBlank(lang.getMaterialColor(), null));
            language.setMaterialTexture(StringUtils.defaultIfBlank(lang.getMaterialTexture(), null));
            language.setUsedPosition(StringUtils.defaultIfBlank(lang.getUsedPosition(), null));
            languages.add(language);
        });

        if (!languages.isEmpty()) {
            extField.setLanguages(languages);
        }

        return JSONObject.toJSONString(extField);
    }

    /**
     * 构建矩阵多语言信息
     * 
     * @param matrixInfo 测试矩阵信息
     * @return JSON格式的多语言信息，如果没有有效语言则返回null
     */
    private String buildMatrixLangInfo(TestDataTestMatrixInfo matrixInfo) {
        List<TestDataTestMatrixLangInfo> languages = matrixInfo.getLanguages();
        if (languages == null || languages.isEmpty()) {
            return null;
        }

        List<TestDataMatrixLangInfo> langs = Lists.newArrayList();
        for (TestDataTestMatrixLangInfo language : languages) {
            LanguageType languageType = LanguageType.findCode(language.getLanguageId());
            if (languageType == null || languageType.getLanguageId() <= 0) {
                continue;
            }

            TestDataMatrixLangInfo lang = new TestDataMatrixLangInfo();
            BeanUtils.copyProperties(language, lang);
            lang.setLanguageId(languageType.getLanguageId());
            lang.setEvaluationAlias(Transcoding.rtfStrEsc(language.getEvaluationAlias()));
            // 处理结论显示信息
            String conclusionDisplay = Transcoding.unicodeToChar(language.getConclusionDisplay());
            if (StringUtils.isBlank(conclusionDisplay)) {
                conclusionDisplay = language.getConclusionDisplay();
            }
            lang.setConclusionDisplay(conclusionDisplay);
            lang.setMethodDesc(Transcoding.rtfStrEsc(language.getMethodDesc()));

            langs.add(lang);
        }

        if (langs.isEmpty()) {
            return null;
        }

        return JSONObject.toJSONString(langs);
    }
}