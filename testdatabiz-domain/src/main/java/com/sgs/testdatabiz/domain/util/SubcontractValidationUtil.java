package com.sgs.testdatabiz.domain.util;

import com.sgs.testdatabiz.core.errorcode.SubcontractErrorCodes;
import com.sgs.testdatabiz.core.exception.ReportDataCheckException;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractQueryReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractReplaceReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractInvalidateReq;
import org.apache.commons.lang3.StringUtils;

/**
 * 分包数据参数校验工具类
 * 
 * 提供分包数据管理相关接口的统一参数校验功能
 * 根据请求对象上的注解要求进行基础参数校验
 * 
 * 主要功能：
 * - 查询请求参数校验
 * - 替换请求参数校验  
 * - 无效化请求参数校验
 * - 统一的错误处理和异常抛出
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class SubcontractValidationUtil {

    /**
     * 校验分包数据查询请求参数
     *
     * 校验规则：
     * - 请求对象不能为空
     * - 订单号不能为空（@NotEmpty）
     * - 实验室代码不能为空（@NotEmpty）
     * - 报告号不能为空（@NotEmpty）
     * - 对象编号为可选字段，不强制校验
     *
     * @param req 查询请求对象
     * @throws ReportDataCheckException 参数校验失败时抛出
     */
    public static void validateQueryRequest(SubcontractQueryReq req) {
        // 检查请求对象是否为空
        if (req == null) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_REQUEST_NULL,
                400,
                "分包数据查询请求对象不能为空"
            );
        }

        // 校验订单号
        if (StringUtils.isEmpty(req.getOrderNo())) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_REQUIRED_MISSING,
                400,
                "订单号不能为空"
            );
        }

        // 校验实验室代码
        if (StringUtils.isEmpty(req.getLabCode())) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_REQUIRED_MISSING,
                400,
                "实验室代码不能为空"
            );
        }

        // 校验报告号
        if (StringUtils.isEmpty(req.getReportNo())) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_REQUIRED_MISSING,
                400,
                "报告号不能为空"
            );
        }

        // 注意：对象编号(objectNo)为可选字段，不进行强制校验
    }

    /**
     * 校验分包数据ReportNo替换请求参数
     *
     * 校验规则：
     * - 请求对象不能为空
     * - 订单号不能为空（@NotEmpty）
     * - 实验室代码不能为空（@NotEmpty）
     * - 原报告号不能为空（@NotEmpty）
     * - 新报告号不能为空（@NotEmpty）
     * - 新报告号与原报告号不能相同
     *
     * @param req 替换请求对象
     * @throws ReportDataCheckException 参数校验失败时抛出
     */
    public static void validateReplaceRequest(SubcontractReplaceReq req) {
        // 检查请求对象是否为空
        if (req == null) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_REQUEST_NULL,
                400,
                "分包数据ReportNo替换请求对象不能为空"
            );
        }

        // 校验订单号
        if (StringUtils.isEmpty(req.getOrderNo())) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_REQUIRED_MISSING,
                400,
                "订单号不能为空"
            );
        }

        // 校验实验室代码
        if (StringUtils.isEmpty(req.getLabCode())) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_REQUIRED_MISSING,
                400,
                "实验室代码不能为空"
            );
        }

        // 校验原报告号
        if (StringUtils.isEmpty(req.getReportNo())) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_REQUIRED_MISSING,
                400,
                "原报告号不能为空"
            );
        }

        // 校验新报告号
        if (StringUtils.isEmpty(req.getNewReportNo())) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_REQUIRED_MISSING,
                400,
                "新报告号不能为空"
            );
        }

        // 校验新旧报告号不能相同
        if (req.getReportNo().equals(req.getNewReportNo())) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_DATA_VALIDATE_FAIL,
                400,
                "新报告号与原报告号相同，无需替换"
            );
        }

        // 注意：SubcontractReplaceReq没有objectNo字段，只有orderNo、labCode、reportNo、newReportNo
    }

    /**
     * 校验分包数据无效化请求参数
     * 
     * 校验规则：
     * - 请求对象不能为空
     * - 订单号不能为空（@NotEmpty）
     * - 对象编号不能为空（@NotEmpty）
     * - 实验室代码不能为空（@NotEmpty）
     * - 报告号不能为空（@NotEmpty）
     * 
     * @param req 无效化请求对象
     * @throws ReportDataCheckException 参数校验失败时抛出
     */
    public static void validateInvalidateRequest(SubcontractInvalidateReq req) {
        // 检查请求对象是否为空
        if (req == null) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_REQUEST_NULL, 
                400, 
                "分包数据无效化请求对象不能为空"
            );
        }

        // 校验订单号
        if (StringUtils.isEmpty(req.getOrderNo())) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_REQUIRED_MISSING, 
                400, 
                "订单号不能为空"
            );
        }

        // 校验对象编号
        if (StringUtils.isEmpty(req.getObjectNo())) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_REQUIRED_MISSING, 
                400, 
                "对象编号不能为空"
            );
        }

        // 校验实验室代码
        if (StringUtils.isEmpty(req.getLabCode())) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_REQUIRED_MISSING, 
                400, "实验室代码不能为空"
            );
        }

        // 校验报告号
        if (StringUtils.isEmpty(req.getReportNo())) {
            throw new ReportDataCheckException(
                SubcontractErrorCodes.SUBCONTRACT_REQUIRED_MISSING, 
                400, 
                "报告号不能为空"
            );
        }
    }
}
