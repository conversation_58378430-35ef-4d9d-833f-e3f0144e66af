package com.sgs.testdatabiz.domain.service.testdata.service.testdata;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.facade.model.testdata.TestDataTestMatrixInfo;

import java.util.List;
import java.util.Map;

/**
 * 测试数据服务接口
 * 负责处理测试数据的构建、转换和管理
 * 
 * <AUTHOR>
 */
public interface TestDataService {
    
    /**
     * 从测试矩阵信息构建测试数据列表
     * 
     * @param testMatrix 测试矩阵信息
     * @param testDataMatrixPO 测试数据矩阵PO对象
     * @return 测试数据映射表，key为BizVersionId，value为TestDataInfoPO
     */
    Map<String, TestDataInfoPO> buildTestDataFromMatrix(TestDataTestMatrixInfo testMatrix, TestDataMatrixInfoPO testDataMatrixPO);
    
    /**
     * 批量构建测试数据列表
     * 
     * @param testMatrices 测试矩阵信息列表
     * @param testDataMatrixPOs 测试数据矩阵PO对象列表
     * @return 所有测试数据的列表
     */
    List<TestDataInfoPO> buildTestDataList(List<TestDataTestMatrixInfo> testMatrices, List<TestDataMatrixInfoPO> testDataMatrixPOs);
    
    /**
     * 与现有测试数据进行协调处理
     * 将新的测试数据与数据库中已存在的数据进行比较和合并
     * 
     * @param newTestData 新的测试数据列表
     * @param objectRelId 对象关系ID
     * @param suffix 数据后缀
     * @return 协调后的测试数据列表
     */
    List<TestDataInfoPO> reconcileWithExistingTestData(List<TestDataInfoPO> newTestData, String objectRelId, String suffix);
}