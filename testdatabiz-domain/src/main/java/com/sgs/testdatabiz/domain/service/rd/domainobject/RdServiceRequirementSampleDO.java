/**
  * Copyright 2023 json.cn
  */
package com.sgs.testdatabiz.domain.service.rd.domainobject;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdDeliveryDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RdServiceRequirementSampleDO implements Serializable {
    private Integer liquid;
    private RdDeliveryDO returnResidueSample;
    private RdDeliveryDO returnTestSample;

    private Date lastModifiedTimestamp;

    private Integer activeIndicator;
}
