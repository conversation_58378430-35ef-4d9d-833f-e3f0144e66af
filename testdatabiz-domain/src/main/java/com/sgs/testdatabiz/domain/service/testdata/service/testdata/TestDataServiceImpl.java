package com.sgs.testdatabiz.domain.service.testdata.service.testdata;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.enums.LanguageType;
import com.sgs.testdatabiz.core.util.DateUtils;
import com.sgs.testdatabiz.core.util.NumberUtil;
import com.sgs.testdatabiz.core.util.SnowflakeIdWorker;
import com.sgs.testdatabiz.core.util.Transcoding;
import com.sgs.testdatabiz.dbstorages.mybatis.enums.ActiveIndicatorEnum;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.domain.service.testdata.model.BizVersionId;
import com.sgs.testdatabiz.domain.service.testdata.repository.TestDataRepository;
import com.sgs.testdatabiz.domain.service.testdata.strategy.TestDataReconciliationStrategy;
import com.sgs.testdatabiz.facade.model.enums.AnalyteTypeEnum;
import com.sgs.testdatabiz.facade.model.info.starlims.TestDataLangInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataResultInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataResultLangInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataTestMatrixInfo;

import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 测试数据服务实现类
 * 负责处理测试数据的构建、转换和管理
 * 
 * <AUTHOR>
 */
@Service
public class TestDataServiceImpl implements TestDataService {
    
    private static final Logger logger = LoggerFactory.getLogger(TestDataServiceImpl.class);
    
    @Autowired
    private SnowflakeIdWorker idWorker;
    
    @Autowired
    private TestDataRepository testDataRepository;
    
    @Autowired
    private TestDataReconciliationStrategy testDataReconciliationStrategy;
    
    @Override
    public Map<String, TestDataInfoPO> buildTestDataFromMatrix(TestDataTestMatrixInfo testMatrix, TestDataMatrixInfoPO testDataMatrixPO) {
        logger.debug("开始从测试矩阵构建测试数据，矩阵ID: {}", testDataMatrixPO.getId());
        
        Map<String, TestDataInfoPO> testDataMaps = Maps.newConcurrentMap();
        
        if (testMatrix == null) {
            logger.warn("测试矩阵信息为空，返回空的测试数据映射");
            return testDataMaps;
        }
        
        List<TestDataResultInfo> testResults = testMatrix.getTestResults();
        if (testResults == null || testResults.isEmpty()) {
            logger.debug("测试矩阵 {} 没有测试结果数据", testDataMatrixPO.getId());
            return testDataMaps;
        }
        
        for (TestDataResultInfo testResult : testResults) {
            TestDataInfoPO testData = buildTestDataFromResult(testResult, testDataMatrixPO);
            
            // 使用BizVersionId生成业务版本ID
            BizVersionId bizVersionId = BizVersionId.fromTestData(testData);
            testData.setBizVersionId(bizVersionId.getValue());
            
            testDataMaps.put(testData.getBizVersionId(), testData);
            
            logger.debug("构建测试数据完成，分析物代码: {}, BizVersionId: {}", 
                    testData.getAnalyteCode(), testData.getBizVersionId());
        }
        
        logger.info("从测试矩阵 {} 构建了 {} 条测试数据", testDataMatrixPO.getId(), testDataMaps.size());
        return testDataMaps;
    }
    
    @Override
    public List<TestDataInfoPO> buildTestDataList(List<TestDataTestMatrixInfo> testMatrices, List<TestDataMatrixInfoPO> testDataMatrixPOs) {
        logger.debug("开始批量构建测试数据列表，矩阵数量: {}", testMatrices.size());
        
        List<TestDataInfoPO> allTestData = Lists.newArrayList();
        
        if (testMatrices == null || testMatrices.isEmpty() || 
            testDataMatrixPOs == null || testDataMatrixPOs.isEmpty()) {
            logger.warn("测试矩阵信息或PO对象列表为空");
            return allTestData;
        }
        
        // 确保两个列表大小一致
        int size = Math.min(testMatrices.size(), testDataMatrixPOs.size());
        
        for (int i = 0; i < size; i++) {
            TestDataTestMatrixInfo testMatrix = testMatrices.get(i);
            TestDataMatrixInfoPO testDataMatrixPO = testDataMatrixPOs.get(i);
            
            Map<String, TestDataInfoPO> testDataMap = buildTestDataFromMatrix(testMatrix, testDataMatrixPO);
            allTestData.addAll(testDataMap.values());
        }
        
        logger.info("批量构建测试数据完成，总计: {} 条", allTestData.size());
        return allTestData;
    }
    
    @Override
    public List<TestDataInfoPO> reconcileWithExistingTestData(List<TestDataInfoPO> newTestData, String objectRelId, String suffix) {
        logger.debug("开始协调测试数据，新数据数量: {}, 对象关系ID: {}, 后缀: {}", 
                newTestData != null ? newTestData.size() : 0, objectRelId, suffix);
        
        if (newTestData == null || newTestData.isEmpty()) {
            logger.warn("新测试数据列表为空，无需协调");
            return Lists.newArrayList();
        }
        
        if (Func.isBlank(objectRelId)) {
            logger.warn("对象关系ID为空，无法查询现有数据");
            return newTestData;
        }
        
        // 查询数据库中已存在的测试数据
        List<TestDataInfoPO> existingTestData = testDataRepository.findExistingTestData(objectRelId, suffix);
        logger.debug("查询到现有测试数据: {} 条", existingTestData.size());
        
        // 使用策略模式进行数据协调
        return testDataReconciliationStrategy.reconcile(objectRelId,suffix,newTestData, existingTestData);
    }
    
    /**
     * 从测试结果信息构建测试数据PO对象
     * 
     * @param testResult 测试结果信息
     * @param testDataMatrixPO 测试数据矩阵PO对象
     * @return 构建的测试数据PO对象
     */
    private TestDataInfoPO buildTestDataFromResult(TestDataResultInfo testResult, TestDataMatrixInfoPO testDataMatrixPO) {
        TestDataInfoPO testData = new TestDataInfoPO();

        // 设置基本信息
        testData.setId(idWorker.nextId());
        testData.setObjectRelId(testDataMatrixPO.getObjectRelId());
        testData.setTestDataMatrixId(testDataMatrixPO.getId());

        // 设置分析物信息
        testData.setAnalyteCode(testResult.getAnalyteCode());
        testData.setAnalyteId(NumberUtil.toInt(testResult.getTestAnalyteId()) > 0 ?
                testResult.getTestAnalyteId().toString() : null);
        testData.setAnalyteName(testResult.getTestAnalyteName());

        // 设置分析物类型
        AnalyteTypeEnum analyteType = AnalyteTypeEnum.findType(testResult.getAnalyteType(), AnalyteTypeEnum.General);
        testData.setAnalyteType(analyteType.getType());

        // 设置其他测试相关信息
        testData.setAnalyteSeq(testResult.getAnalyteSeq());
        testData.setReportUnit(testResult.getReportUnit());
        testData.setLimitUnit(testResult.getLimitUnit());
        testData.setTestValue(testResult.getTestValue());
        testData.setCasNo(testResult.getCasNo());
        testData.setReportLimit(testResult.getReportLimit());
        testData.setConclusionId(testResult.getConclusionId());
        testData.setTestResultType(testResult.getTestResultType());
        testData.setConditionName(testResult.getConditionName());
        testData.setParentConditionName(testResult.getParentConditionName());
        testData.setProcedureConditionName(testResult.getProcedureConditionName());
        testData.setSpecimenDesc(testResult.getSpecimenDesc());
        testData.setUpSpecimenDesc(testResult.getUpSpecimenDesc());
        testData.setTestDataId(testResult.getTestDataId());
        testData.setPosition(testResult.getPosition());

        // 设置多语言信息
        testData.setLanguages(getResultLangInfo(testResult));

        // 设置审计信息
        testData.setActiveIndicator(ActiveIndicatorEnum.ACTIVE.getValue());
        testData.setCreatedBy(testDataMatrixPO.getCreatedBy());
        testData.setCreatedDate(DateUtils.getNow());
        testData.setModifiedBy(testDataMatrixPO.getModifiedBy());
        testData.setModifiedDate(DateUtils.getNow());

        // SCI-1378: methodLimit对应的是LimitValueFullName
        testData.setLimitValueFullName(testResult.getMethodLimit());

        return testData;
    }

    /**
     * @param td
     * @return
     */
    private String getResultLangInfo(TestDataResultInfo td) {
        List<TestDataResultLangInfo> languages = td.getLanguages();
        if (languages == null || languages.isEmpty()) {
            return null;
        }
        List<TestDataLangInfo> langs = Lists.newArrayList();
        for (TestDataResultLangInfo language : languages) {
            LanguageType languageType = LanguageType.findCode(language.getLanguageId());
            if (languageType == null || languageType.getLanguageId() <= 0) {
                continue;
            }
            TestDataLangInfo lang = buildTestDataLangInfo(language, languageType);
            langs.add(lang);
        }
        if (langs.isEmpty()) {
            return null;
        }
        return JSONObject.toJSONString(langs);
    }

    @NotNull
    private static TestDataLangInfo buildTestDataLangInfo(TestDataResultLangInfo language, LanguageType languageType) {
        TestDataLangInfo lang = new TestDataLangInfo();
        lang.setLanguageId(languageType.getLanguageId());
        String analyteName = Transcoding.unicodeToChar(language.getTestAnalyteName());
        if (StringUtils.isBlank(analyteName)) {
            analyteName = language.getTestAnalyteName();
        }
        lang.setTestAnalyteName(analyteName);

        String reportUnit = Transcoding.unicodeToChar(language.getReportUnit());
        if (StringUtils.isBlank(reportUnit)) {
            reportUnit = language.getReportUnit();
        }
        lang.setReportUnit(reportUnit);

        String specimenDesc = Transcoding.unicodeToChar(language.getSpecimenDesc());
        if (StringUtils.isBlank(specimenDesc)) {
            specimenDesc = language.getSpecimenDesc();
        }
        lang.setSpecimenDesc(specimenDesc);

        String conditionName = Transcoding.unicodeToChar(language.getConditionName());
        if (StringUtils.isBlank(conditionName)) {
            conditionName = language.getConditionName();
        }
        lang.setConditionName(conditionName);

        String upSpecimenDesc = Transcoding.unicodeToChar(language.getUpSpecimenDesc());
        if (StringUtils.isBlank(upSpecimenDesc)) {
            upSpecimenDesc = language.getUpSpecimenDesc();
        }
        lang.setUpSpecimenDesc(upSpecimenDesc);

        String procedureConditionName = Transcoding.unicodeToChar(language.getProcedureConditionName());
        if (StringUtils.isBlank(procedureConditionName)) {
            procedureConditionName = language.getProcedureConditionName();
        }
        lang.setProcedureConditionName(procedureConditionName);

        String parentConditionName = Transcoding.unicodeToChar(language.getParentConditionName());
        if (StringUtils.isBlank(parentConditionName)) {
            parentConditionName = language.getParentConditionName();
        }
        lang.setParentConditionName(parentConditionName);

        String position = Transcoding.unicodeToChar(language.getPosition());
        if (StringUtils.isBlank(position)) {
            position = language.getPosition();
        }
        lang.setPosition(position);
        return lang;
    }
}