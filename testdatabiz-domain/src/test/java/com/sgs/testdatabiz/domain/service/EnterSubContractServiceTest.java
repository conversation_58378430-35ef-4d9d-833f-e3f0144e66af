package com.sgs.testdatabiz.domain.service;

import com.sgs.framework.core.base.CustomResult;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataMatrixInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataObjectRelExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.SubcontractQueryDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractQueryReq;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataTestMatrixInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * EnterSubContractService测试类
 * 
 * 主要测试querySubcontractData方法的修改，验证能否正确返回完整的ReportTestDataInfo对象
 */
@ExtendWith(MockitoExtension.class)
class EnterSubContractServiceTest {

    @Mock
    private TestDataObjectRelExtMapper objectRelExtMapper;
    
    @Mock
    private TestDataMatrixInfoExtMapper matrixInfoExtMapper;
    
    @Mock
    private TestDataInfoExtMapper testDataInfoExtMapper;

    @InjectMocks
    private EnterSubContractService enterSubContractService;

    private SubcontractQueryReq queryReq;
    private TestDataObjectRelPO mockObjectRel;
    private TestDataMatrixInfoPO mockMatrixInfo;
    private TestDataInfoPO mockTestData;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        queryReq = new SubcontractQueryReq();
        queryReq.setOrderNo("TEST-ORDER-001");
        queryReq.setLabCode("GZ SL");
        queryReq.setReportNo("TEST-REPORT-001");

        // 模拟TestDataObjectRelPO
        mockObjectRel = new TestDataObjectRelPO();
        mockObjectRel.setId("test-object-rel-id");
        mockObjectRel.setOrderNo("TEST-ORDER-001");
        mockObjectRel.setLabCode("GZ SL");
        mockObjectRel.setReportNo("TEST-REPORT-001");
        mockObjectRel.setObjectNo("TEST-SUBCONTRACT-001");
        mockObjectRel.setSourceType(6); // Enter subcontract
        mockObjectRel.setLanguageId(1); // English
        mockObjectRel.setCompleteDate(new Date());

        // 模拟TestDataMatrixInfoPO
        mockMatrixInfo = new TestDataMatrixInfoPO();
        mockMatrixInfo.setId(1L);
        mockMatrixInfo.setObjectRelId("test-object-rel-id"); // 设置关联关系
        mockMatrixInfo.setTestMatrixId("TEST-MATRIX-001");
        mockMatrixInfo.setTestLineId(100);
        mockMatrixInfo.setSampleNo("SAMPLE-001");

        // 模拟TestDataInfoPO
        mockTestData = new TestDataInfoPO();
        mockTestData.setAnalyteId(1L);
        mockTestData.setTestDataMatrixId(1L); // 设置关联关系
        mockTestData.setAnalyteName("Test Analyte");
        mockTestData.setTestValue("10.5");
        mockTestData.setReportUnit("mg/kg");
        mockTestData.setAnalyteSeq(1);
    }

    @Test
    void testQuerySubcontractData_Success() {
        // 准备Mock数据
        when(objectRelExtMapper.queryValidSubcontractData(any(SubcontractQueryDTO.class)))
                .thenReturn(Arrays.asList(mockObjectRel));
        when(matrixInfoExtMapper.queryMatrixList(any(List.class), anyString()))
                .thenReturn(Arrays.asList(mockMatrixInfo));
        when(testDataInfoExtMapper.queryTestDataInfoList(any(List.class), anyString()))
                .thenReturn(Arrays.asList(mockTestData));

        // 执行测试
        CustomResult<List<ReportTestDataInfo>> result = enterSubContractService.querySubcontractData(queryReq);

        // 验证结果
        assertTrue(result.isSuccess(), "查询应该成功");
        assertNotNull(result.getData(), "返回数据不应为空");
        assertEquals(1, result.getData().size(), "应该返回1条数据");

        ReportTestDataInfo reportTestDataInfo = result.getData().get(0);
        
        // 验证基本信息
        assertEquals("TEST-ORDER-001", reportTestDataInfo.getOrderNo(), "订单号应该正确");
        assertEquals("GZ SL", reportTestDataInfo.getLabCode(), "实验室代码应该正确");
        assertEquals("TEST-REPORT-001", reportTestDataInfo.getReportNo(), "报告号应该正确");
        assertEquals("TEST-SUBCONTRACT-001", reportTestDataInfo.getSubContractNo(), "分包号应该正确");
        assertEquals(Integer.valueOf(6), reportTestDataInfo.getSourceType(), "数据来源类型应该正确");

        // 验证测试矩阵数据
        assertNotNull(reportTestDataInfo.getTestMatrixs(), "测试矩阵列表不应为空");
        assertEquals(1, reportTestDataInfo.getTestMatrixs().size(), "应该有1个测试矩阵");
        
        TestDataTestMatrixInfo testMatrix = reportTestDataInfo.getTestMatrixs().get(0);
        assertEquals("TEST-MATRIX-001", testMatrix.getTestMatrixId(), "测试矩阵ID应该正确");
        assertEquals(Integer.valueOf(100), testMatrix.getTestLineId(), "测试线ID应该正确");
        assertEquals("SAMPLE-001", testMatrix.getTestSampleNo(), "样品编号应该正确");

        // 验证测试数据
        assertNotNull(testMatrix.getTestResults(), "测试结果列表不应为空");
        assertEquals(1, testMatrix.getTestResults().size(), "应该有1个测试结果");
        assertEquals("Test Analyte", testMatrix.getTestResults().get(0).getAnalyteName(), "分析项名称应该正确");
        assertEquals("10.5", testMatrix.getTestResults().get(0).getTestValue(), "测试值应该正确");
    }

    @Test
    void testQuerySubcontractData_NoDataFound() {
        // 模拟没有查询到数据的情况
        when(objectRelExtMapper.queryValidSubcontractData(any(SubcontractQueryDTO.class)))
                .thenReturn(Arrays.asList());

        // 执行测试
        CustomResult<List<ReportTestDataInfo>> result = enterSubContractService.querySubcontractData(queryReq);

        // 验证结果
        assertTrue(result.isSuccess(), "查询应该成功");
        assertNotNull(result.getData(), "返回数据不应为空");
        assertEquals(0, result.getData().size(), "应该返回0条数据");
    }

    @Test
    void testQuerySubcontractData_BatchOptimization() {
        // 准备多条测试数据来验证批量查询优化
        TestDataObjectRelPO mockObjectRel2 = new TestDataObjectRelPO();
        mockObjectRel2.setId("test-object-rel-id-2");
        mockObjectRel2.setOrderNo("TEST-ORDER-002");
        mockObjectRel2.setLabCode("GZ SL"); // 相同的labCode
        mockObjectRel2.setReportNo("TEST-REPORT-002");
        mockObjectRel2.setObjectNo("TEST-SUBCONTRACT-002");
        mockObjectRel2.setSourceType(6);
        mockObjectRel2.setLanguageId(1);
        mockObjectRel2.setCompleteDate(new Date());

        TestDataMatrixInfoPO mockMatrixInfo2 = new TestDataMatrixInfoPO();
        mockMatrixInfo2.setId(2L);
        mockMatrixInfo2.setObjectRelId("test-object-rel-id-2");
        mockMatrixInfo2.setTestMatrixId("TEST-MATRIX-002");
        mockMatrixInfo2.setTestLineId(200);
        mockMatrixInfo2.setSampleNo("SAMPLE-002");

        TestDataInfoPO mockTestData2 = new TestDataInfoPO();
        mockTestData2.setAnalyteId(2L);
        mockTestData2.setTestDataMatrixId(2L);
        mockTestData2.setAnalyteName("Test Analyte 2");
        mockTestData2.setTestValue("20.5");
        mockTestData2.setReportUnit("ppm");
        mockTestData2.setAnalyteSeq(2);

        // 准备Mock数据 - 返回多条记录
        when(objectRelExtMapper.queryValidSubcontractData(any(SubcontractQueryDTO.class)))
                .thenReturn(Arrays.asList(mockObjectRel, mockObjectRel2));
        when(matrixInfoExtMapper.queryMatrixList(any(List.class), anyString()))
                .thenReturn(Arrays.asList(mockMatrixInfo, mockMatrixInfo2));
        when(testDataInfoExtMapper.queryTestDataInfoList(any(List.class), anyString()))
                .thenReturn(Arrays.asList(mockTestData, mockTestData2));

        // 执行测试
        CustomResult<List<ReportTestDataInfo>> result = enterSubContractService.querySubcontractData(queryReq);

        // 验证结果
        assertTrue(result.isSuccess(), "查询应该成功");
        assertNotNull(result.getData(), "返回数据不应为空");
        assertEquals(2, result.getData().size(), "应该返回2条数据");

        // 验证批量查询只调用了一次（而不是N+1次）
        verify(matrixInfoExtMapper, times(1)).queryMatrixList(any(List.class), anyString());
        verify(testDataInfoExtMapper, times(1)).queryTestDataInfoList(any(List.class), anyString());

        // 验证数据正确性
        ReportTestDataInfo firstData = result.getData().get(0);
        ReportTestDataInfo secondData = result.getData().get(1);

        assertEquals("TEST-ORDER-001", firstData.getOrderNo());
        assertEquals("TEST-ORDER-002", secondData.getOrderNo());

        assertEquals(1, firstData.getTestMatrixs().size());
        assertEquals(1, secondData.getTestMatrixs().size());
    }
}
