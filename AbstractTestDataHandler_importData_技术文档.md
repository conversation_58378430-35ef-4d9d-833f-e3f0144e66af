# AbstractTestDataHandler.importData 方法技术文档

## 概述

`importData` 方法是 `AbstractTestDataHandler` 类中的核心方法，负责处理测试数据的导入逻辑。该方法接收 `ReportTestDataInfo` 对象作为输入，经过数据校验和处理后，将测试数据保存到数据库中。

## 方法签名

```java
public final BaseResponse<Void> importData(ReportTestDataInfo reportTestDataInfo)
```

## 输入参数

- `reportTestDataInfo`: `ReportTestDataInfo` 类型，包含待导入的测试报告数据信息

## 返回值

- `BaseResponse<Void>`: 标准响应对象，包含操作结果状态码和消息

## 核心处理流程

### 1. 初始化阶段

```java
BaseResponse<Void> response = BaseResponse.newFailInstance(ResponseCode.FAIL);
String suffix = StringUtil.getTestDataSuffix(reportTestDataInfo.getLabCode());
tableNameChecker.checkTableNameExist(reportTestDataInfo.getLabCode());
setChannel(SourceTypeUtils.toSourceTypeEnum(reportTestDataInfo.getSourceType()));
```

**处理逻辑：**
- 创建默认失败响应对象
- 根据实验室代码获取测试数据后缀
- 检查表名是否存在
- 设置数据来源渠道到上下文中

### 2. 数据校验阶段

```java
boolean checkFailed = !checkData(reportTestDataInfo);
if (checkFailed) {
    response.setStatus(ResponseCode.ILLEGAL_ARGUMENT.getCode());
    response.setMessage("参数校验失败");
    return response;
}
```

**处理逻辑：**
- 调用 `checkData` 方法进行数据校验
- 如果校验失败，返回参数校验失败的响应

### 3. 数据保存阶段

```java
boolean saveSuccess = this.saveReportTestData(reportTestDataInfo);
if (saveSuccess) {
    return new BaseResponse(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage());
} else {
    response.setStatus(ResponseCode.ILLEGAL_ARGUMENT.getCode());
    response.setMessage("保存testData失败");
    return response;
}
```

**处理逻辑：**
- 调用 `saveReportTestData` 方法保存测试数据
- 保存成功返回成功响应，失败返回保存失败响应

## 异常处理机制

### 1. 业务异常处理

```java
if(e instanceof ReportDataCheckException){
    ReportDataCheckException reportDataCheckException = (ReportDataCheckException) e;
    return BaseResponse.fail(ResponseCode.ILLEGAL_ARGUMENT.getCode(), reportDataCheckException.getMessage());
}
```

**处理逻辑：**
- 捕获 `ReportDataCheckException` 业务异常
- 返回带有具体错误信息的失败响应

### 2. 通用异常处理

```java
catch (Exception e) {
    logger.error("importData err.", e);
    return BaseResponse.newFailInstance(ResponseCode.UNKNOWN);
}
```

**处理逻辑：**
- 记录异常日志
- 返回未知错误响应

## Finally 块处理

### 1. 错误消息附加

```java
String errMsg = formatErrMsg(getErrorMsg());
if (!response.isSuccess() && StringUtils.isNotEmpty(errMsg)) {
    response.setMessage(errMsg);
}
```

**处理逻辑：**
- 格式化错误消息
- 如果响应失败且有错误消息，则附加到响应中

### 2. 异步处理

```java
// 邮件告警错误信息(异步)
mailAlarmService.asyncSendEMailIfy(reportTestDataInfo, errMsg);
// 记录错误信息到数据库(异步)
readXmlLogService.asyncBatchSaveLog(reportTestDataInfo, errMsg);
```

**处理逻辑：**
- 异步发送邮件告警
- 异步记录错误日志到数据库

### 3. 上下文清理

```java
this.clearContext();
```

**处理逻辑：**
- 清理线程上下文信息

## 关键依赖方法

### 1. checkData 方法

- **功能**: 对测试数据进行业务校验
- **返回**: boolean 类型，true 表示校验通过
- **校验内容**: 
  - 测试矩阵数据完整性
  - 订单中测试矩阵存在性
  - 分析物代码有效性
  - 测试结果数据有效性

### 2. saveReportTestData 方法

- **功能**: 保存测试报告数据到数据库
- **返回**: boolean 类型，true 表示保存成功
- **实现**: 调用 `reportTestDataService.saveTestData(reqObject).isSuccess()`

### 3. formatErrMsg 方法

- **功能**: 格式化错误消息
- **参数**: `Collection<ErrorMsg>` 错误消息集合
- **返回**: 格式化后的错误消息字符串

## 上下文管理

### 1. 渠道设置

```java
private void setChannel(SourceTypeEnum sourceType) {
    TestDataImportContext.setChannel(sourceType);
}
```

### 2. 上下文清理

```java
private void clearContext() {
    TestDataImportContext.clear();
}
```

## 日志记录

- **入口日志**: `logger.info("TestData.importData: [{}]", ...)`
- **异常日志**: `logger.error("importData err.", e)`

## 响应码说明

| 响应码 | 含义 | 触发条件 |
|--------|------|----------|
| SUCCESS | 成功 | 数据保存成功 |
| FAIL | 失败 | 默认失败状态 |
| ILLEGAL_ARGUMENT | 参数错误 | 数据校验失败或保存失败 |
| UNKNOWN | 未知错误 | 捕获到未预期异常 |

## 性能考虑

1. **异步处理**: 邮件告警和日志记录采用异步方式，避免阻塞主流程
2. **上下文管理**: 使用 ThreadLocal 管理上下文，确保线程安全
3. **资源清理**: Finally 块确保上下文资源得到及时清理

## 扩展点

1. **数据校验**: `checkData` 方法可根据不同业务需求进行扩展
2. **错误处理**: `formatErrMsg` 方法支持自定义错误消息格式
3. **异步处理**: 邮件告警和日志记录服务可根据需要扩展

## 注意事项

1. 方法使用 `final` 修饰，不允许子类重写
2. 异常处理区分业务异常和系统异常
3. 上下文信息需要在 finally 块中清理，防止内存泄漏
4. 异步处理可能存在延迟，需要考虑监控和重试机制

## TODO 项

代码中标注的 TODO 项：
- 改成本地事件模式（邮件告警和日志记录）

这表明未来可能会将异步处理改为基于事件的架构模式。