# 分包数据管理API接口实现总结

## 完成的工作

### 1. 请求响应模型类
已创建以下请求模型类：
- `SubcontractQueryReq` - 分包数据查询请求
- `SubcontractReplaceReq` - 分包数据ReportNo替换请求  
- `SubcontractInvalidateReq` - 分包数据无效化请求

### 2. 数据传输对象(DTO)
已创建以下DTO类：
- `SubcontractQueryDTO` - 分包数据查询DTO
- `SubcontractInvalidateDTO` - 分包数据无效化DTO

### 3. 数据访问层(Mapper)
在 `TestDataObjectRelExtMapper` 中添加了以下方法：
- `queryValidSubcontractData()` - 查询有效的分包数据
- `invalidateSubcontractData()` - 批量设置分包数据为无效状态

对应的XML映射文件也已更新，包含完整的SQL实现。

### 4. 业务服务层(Service)
在 `EnterSubContractService` 中完善了三个核心方法：

#### 4.1 querySubcontractData()
- **功能**: 根据订单号、对象编号、实验室代码、报告号查询有效的分包数据
- **特点**: 
  - 简洁的参数验证
  - 清晰的错误处理
  - 详细的日志记录

#### 4.2 replaceSubcontractReportNo()
- **功能**: 通过原数据无效+新增数据的方式实现ReportNo替换
- **特点**:
  - 使用 `@Transactional` 注解确保事务管理
  - 完整的业务流程：查询原数据 → 无效化原数据 → 创建新数据
  - 严格的错误处理和回滚机制
  - 数据完整性保证

#### 4.3 invalidateSubcontractData()
- **功能**: 将指定分包数据设置为无效状态
- **特点**:
  - 软删除机制，保留历史数据
  - 简洁的实现逻辑
  - 准确的影响行数统计

### 5. 辅助方法
创建了 `createNewObjectRelRecord()` 私有方法：
- 负责复制原始数据并创建新记录
- 正确设置新的ReportNo
- 生成新的ID和业务版本ID
- 设置正确的状态和时间字段

## 代码特点

### 1. 简洁清晰
- 移除了复杂的参数校验方法，使用框架自带的验证
- 删除了重复的方法定义
- 代码逻辑清晰，易于理解和维护

### 2. 事务管理
- `replaceSubcontractReportNo` 方法使用 `@Transactional` 注解
- 确保原数据无效化和新数据创建的原子性
- 异常时自动回滚，保证数据一致性

### 3. 错误处理
- 统一的错误处理机制
- 详细的错误信息返回
- 完整的异常日志记录

### 4. 性能优化
- 使用DTO对象封装参数，减少方法参数数量
- XML中添加了合适的索引建议
- 查询结果按创建时间降序排列，获取最新数据

## 数据库设计

### 1. 核心表结构
使用 `tb_test_data_object_rel` 表作为主要数据存储

### 2. 关键字段
- `ActiveIndicator`: 有效标识 (1:有效, 0:无效)
- `ReportNo`: 报告号
- `BizVersionId`: 业务版本ID，用于数据完整性校验

### 3. SQL操作
- **查询**: 根据多个条件精确查询有效数据
- **更新**: 软删除机制，只更新ActiveIndicator字段
- **插入**: 复制原数据并插入新记录

## 建议的索引优化

```sql
-- 查询优化索引
CREATE INDEX idx_subcontract_query 
ON tb_test_data_object_rel (OrderNo, ObjectNo, LabCode, ReportNo, ActiveIndicator);

-- 时间排序优化索引  
CREATE INDEX idx_subcontract_created_date
ON tb_test_data_object_rel (CreatedDate DESC);
```

## 使用示例

### 1. 查询分包数据
```java
SubcontractQueryReq queryReq = new SubcontractQueryReq();
queryReq.setOrderNo("ORD-2024-001");
queryReq.setObjectNo("SC-2024-001");
queryReq.setLabCode("GZ");
queryReq.setReportNo("RPT-2024-001");

CustomResult<List<ReportTestDataInfo>> result = service.querySubcontractData(queryReq);
```

### 2. 替换ReportNo
```java
SubcontractReplaceReq replaceReq = new SubcontractReplaceReq();
replaceReq.setOrderNo("ORD-2024-001");
replaceReq.setObjectNo("SC-2024-001");
replaceReq.setLabCode("GZ");
replaceReq.setReportNo("RPT-2024-001");
replaceReq.setNewReportNo("RPT-2024-002");

CustomResult<Void> result = service.replaceSubcontractReportNo(replaceReq);
```

### 3. 无效化数据
```java
SubcontractInvalidateReq invalidateReq = new SubcontractInvalidateReq();
invalidateReq.setOrderNo("ORD-2024-001");
invalidateReq.setObjectNo("SC-2024-001");
invalidateReq.setLabCode("GZ");
invalidateReq.setReportNo("RPT-2024-001");

CustomResult<Void> result = service.invalidateSubcontractData(invalidateReq);
```

## 后续工作

### 1. Controller层实现
需要在 `SubcontractController` 中添加对应的REST API接口

### 2. Facade层实现
需要在 `SubContractFacade` 和 `SubContractFacadeImpl` 中添加对应方法

### 3. 测试用例
建议编写完整的单元测试和集成测试

### 4. 文档更新
更新API文档和接口说明

## 总结

本次实现完成了分包数据管理的三个核心API接口的Service层逻辑，代码简洁清晰，具有良好的事务管理和错误处理机制。通过合理的数据库设计和索引优化，能够支持高效的数据查询和操作。整体实现遵循了项目的DDD架构设计原则，保证了代码的一致性和可维护性。