<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    
    <!-- 各模块的渐变色 -->
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:0.9" />
    </linearGradient>
    
    <linearGradient id="archGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:0.9" />
    </linearGradient>
    
    <linearGradient id="toolGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:0.9" />
    </linearGradient>
    
    <linearGradient id="upgradeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:0.9" />
    </linearGradient>
    
    <linearGradient id="opsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:0.9" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="800" fill="url(#bgGradient)"/>
  
  <!-- 标题 -->
  <text x="600" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="#1e293b">
    AI能力在企业架构中的全方位应用
  </text>
  
  <!-- 中心AI核心 -->
  <circle cx="600" cy="400" r="80" fill="url(#aiGradient)" filter="url(#shadow)"/>
  <text x="600" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
    AI核心能力
  </text>
  <text x="600" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="white">
    智能分析 · 自动化 · 预测
  </text>
  
  <!-- 架构设计模块 -->
  <rect x="100" y="120" width="200" height="120" rx="15" fill="url(#archGradient)" filter="url(#shadow)"/>
  <text x="200" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
    架构设计
  </text>
  <text x="200" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    • DDD领域建模
  </text>
  <text x="200" y="195" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    • 分层架构设计
  </text>
  <text x="200" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    • 模块依赖优化
  </text>
  
  <!-- 工具平台模块 -->
  <rect x="900" y="120" width="200" height="120" rx="15" fill="url(#toolGradient)" filter="url(#shadow)"/>
  <text x="1000" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
    工具平台
  </text>
  <text x="1000" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    • 代码生成工具
  </text>
  <text x="1000" y="195" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    • 测试自动化
  </text>
  <text x="1000" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    • 部署流水线
  </text>
  
  <!-- 架构升级模块 -->
  <rect x="100" y="560" width="200" height="120" rx="15" fill="url(#upgradeGradient)" filter="url(#shadow)"/>
  <text x="200" y="590" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
    架构升级
  </text>
  <text x="200" y="615" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    • 技术栈演进
  </text>
  <text x="200" y="635" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    • 性能优化
  </text>
  <text x="200" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    • 重构建议
  </text>
  
  <!-- 日常运维模块 -->
  <rect x="900" y="560" width="200" height="120" rx="15" fill="url(#opsGradient)" filter="url(#shadow)"/>
  <text x="1000" y="590" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">
    日常运维
  </text>
  <text x="1000" y="615" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    • 智能监控
  </text>
  <text x="1000" y="635" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    • 故障预测
  </text>
  <text x="1000" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    • 自动修复
  </text>
  
  <!-- 连接线和箭头 -->
  <!-- AI核心到架构设计 -->
  <line x1="540" y1="350" x2="280" y2="200" stroke="#64748b" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="400" y="270" font-family="Arial, sans-serif" font-size="12" fill="#475569">
    智能建模分析
  </text>
  
  <!-- AI核心到工具平台 -->
  <line x1="660" y1="350" x2="920" y2="200" stroke="#64748b" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="800" y="270" font-family="Arial, sans-serif" font-size="12" fill="#475569">
    自动化工具链
  </text>
  
  <!-- AI核心到架构升级 -->
  <line x1="540" y1="450" x2="280" y2="600" stroke="#64748b" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="400" y="530" font-family="Arial, sans-serif" font-size="12" fill="#475569">
    演进路径规划
  </text>
  
  <!-- AI核心到日常运维 -->
  <line x1="660" y1="450" x2="920" y2="600" stroke="#64748b" stroke-width="3" marker-end="url(#arrowhead)"/>
  <text x="800" y="530" font-family="Arial, sans-serif" font-size="12" fill="#475569">
    智能运维决策
  </text>
  
  <!-- 模块间的协作连接 -->
  <!-- 架构设计到工具平台 -->
  <path d="M 300 180 Q 600 100 900 180" stroke="#94a3b8" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
  <text x="600" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#64748b">
    设计驱动工具开发
  </text>
  
  <!-- 工具平台到日常运维 -->
  <line x1="1000" y1="240" x2="1000" y2="560" stroke="#94a3b8" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="1020" y="400" font-family="Arial, sans-serif" font-size="11" fill="#64748b">
    工具支撑运维
  </text>
  
  <!-- 日常运维到架构升级 -->
  <path d="M 900 620 Q 600 700 300 620" stroke="#94a3b8" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
  <text x="600" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#64748b">
    运维反馈驱动升级
  </text>
  
  <!-- 架构升级到架构设计 -->
  <line x1="200" y1="560" x2="200" y2="240" stroke="#94a3b8" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="120" y="400" font-family="Arial, sans-serif" font-size="11" fill="#64748b" transform="rotate(-90 120 400)">
    升级完善设计
  </text>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#64748b"/>
    </marker>
  </defs>
  
  <!-- 底部说明 -->
  <rect x="50" y="720" width="1100" height="60" rx="10" fill="#f1f5f9" stroke="#cbd5e1" stroke-width="1"/>
  <text x="600" y="740" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#334155">
    AI能力全方位赋能企业架构生命周期
  </text>
  <text x="600" y="760" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#64748b">
    从架构设计、工具平台建设、架构升级到日常运维，AI提供智能化支持，形成闭环优化体系
  </text>
</svg>
