# 分包数据管理API接口开发设计文档

## 文档信息

| 项目名称 | testdatabiz.iapi.sgs.net |
|---------|-------------------------|
| 功能模块 | 分包数据管理API接口扩展 |
| 文档版本 | v1.0 |
| 创建日期 | 2025-09-11 |
| 文档类型 | 开发设计文档 |

## 1. 项目概述

### 1.1 需求背景

Report Data (RD) 系统作为SGS的业务数据存储服务系统，当前已具备分包数据导入能力。为进一步完善分包数据管理功能，需要在现有的 `SubcontractController` 基础上扩展三个新的API接口能力：

1. **分包数据查询能力** - 根据业务条件查询有效的分包数据
2. **分包数据ReportNo替换能力** - 通过原数据无效+新增数据的方式实现ReportNo替换
3. **分包数据无效化能力** - 将指定分包数据设置为无效状态

### 1.2 业务价值

- 提供分包数据的灵活查询能力，支持精确定位业务数据
- 支持报告号变更场景，保证数据追溯性和完整性
- 提供数据生命周期管理能力，支持数据软删除

### 1.3 技术架构

项目基于以下技术栈：
- **后端框架**: Spring Boot 2.4.2, Java 8+
- **服务框架**: Dubbo 2.8.4
- **数据库**: MySQL 5.1.31, MyBatis ORM
- **架构模式**: DDD领域驱动设计
- **构建工具**: Maven 3.x

## 2. 系统架构设计

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "Web Layer"
        SC[SubcontractController]
    end
    
    subgraph "Facade Layer"
        SCF[SubContractFacade]
        SCFI[SubContractFacadeImpl]
    end
    
    subgraph "Domain Layer"
        ESCS[EnterSubContractService]
    end
    
    subgraph "Data Access Layer"
        TOREM[TestDataObjectRelExtMapper]
        TORM[TestDataObjectRelMapper]
    end
    
    subgraph "Database"
        TDOR[(tb_test_data_object_rel)]
    end
    
    SC --> SCF
    SCF --> SCFI
    SCFI --> ESCS
    ESCS --> TOREM
    ESCS --> TORM
    TOREM --> TDOR
    TORM --> TDOR
```

### 2.2 数据流转架构

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as SubcontractController
    participant Facade as SubContractFacade
    participant Service as EnterSubContractService
    participant Mapper as TestDataObjectRelExtMapper
    participant DB as MySQL数据库
    
    Client->>Controller: HTTP请求
    Controller->>Facade: 调用Facade方法
    Facade->>Service: 调用业务逻辑
    Service->>Mapper: 数据库操作
    Mapper->>DB: SQL执行
    DB-->>Mapper: 返回结果
    Mapper-->>Service: 返回数据
    Service-->>Facade: 返回业务结果
    Facade-->>Controller: 返回响应
    Controller-->>Client: HTTP响应
```

## 3. API接口设计

### 3.1 分包数据查询接口

#### 接口基本信息
- **接口路径**: `POST /subContract/querySubcontractData`
- **接口描述**: 根据订单号、对象编号、实验室代码、报告号查询有效的分包数据
- **接口类型**: 查询接口

#### 请求参数 `SubcontractQueryReq`
```json
{
  "orderNo": "ORD-2024-001",
  "objectNo": "SC-2024-001", 
  "labCode": "GZ",
  "reportNo": "RPT-2024-001"
}
```

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| orderNo | String | 是 | 订单号 | ORD-2024-001 |
| objectNo | String | 是 | 对象编号/分包号 | SC-2024-001 |
| labCode | String | 是 | 实验室代码 | GZ |
| reportNo | String | 是 | 报告号 | RPT-2024-001 |

#### 响应参数 `BaseResponse<List<ReportTestDataInfo>>`
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "orderNo": "ORD-2024-001",
      "parentOrderNo": "PORD-2024-001",
      "subContractNo": "SC-2024-001",
      "reportNo": "RPT-2024-001",
      "objectNo": "SC-2024-001",
      "labCode": "GZ",
      "productLineCode": "CTS",
      "externalId": "EXT-001",
      "externalNo": "SLIM-001",
      "externalObjectNo": "SLIM-OBJ-001",
      "completedDate": "2024-01-10T10:30:00",
      "sourceType": "5",
      "languageId": 1,
      "testMatrixs": []
    }
  ]
}
```

### 3.2 分包数据ReportNo替换接口

#### 接口基本信息
- **接口路径**: `POST /subContract/replaceSubcontractReportNo`
- **接口描述**: 将原分包数据设置为无效，并新增一条使用新ReportNo的数据
- **接口类型**: 业务操作接口

#### 请求参数 `SubcontractReplaceReq`
```json
{
  "orderNo": "ORD-2024-001",
  "objectNo": "SC-2024-001",
  "labCode": "GZ", 
  "reportNo": "RPT-2024-001",
  "newReportNo": "RPT-2024-002"
}
```

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| orderNo | String | 是 | 订单号 | ORD-2024-001 |
| objectNo | String | 是 | 对象编号/分包号 | SC-2024-001 |
| labCode | String | 是 | 实验室代码 | GZ |
| reportNo | String | 是 | 原报告号 | RPT-2024-001 |
| newReportNo | String | 是 | 新报告号 | RPT-2024-002 |

#### 响应参数 `BaseResponse<Void>`
```json
{
  "code": 200,
  "message": "分包数据ReportNo替换成功",
  "data": null
}
```

### 3.3 分包数据无效化接口

#### 接口基本信息
- **接口路径**: `POST /subContract/invalidateSubcontractData`
- **接口描述**: 将指定分包数据设置为无效状态
- **接口类型**: 状态管理接口

#### 请求参数 `SubcontractInvalidateReq`
```json
{
  "orderNo": "ORD-2024-001",
  "objectNo": "SC-2024-001",
  "labCode": "GZ",
  "reportNo": "RPT-2024-001"
}
```

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| orderNo | String | 是 | 订单号 | ORD-2024-001 |
| objectNo | String | 是 | 对象编号/分包号 | SC-2024-001 |
| labCode | String | 是 | 实验室代码 | GZ |
| reportNo | String | 是 | 报告号 | RPT-2024-001 |

#### 响应参数 `BaseResponse<Void>`
```json
{
  "code": 200,
  "message": "分包数据无效化成功",
  "data": null
}
```

## 4. 业务流程设计

### 4.1 分包数据查询流程

```mermaid
flowchart TD
    A[接收查询请求] --> B{参数验证}
    B -->|失败| C[返回参数错误]
    B -->|成功| D[构建查询条件]
    D --> E[执行数据库查询]
    E --> F{查询结果}
    F -->|无数据| G[返回空列表]
    F -->|有数据| H[转换为业务对象]
    H --> I[返回查询结果]
```

### 4.2 ReportNo替换流程

```mermaid
flowchart TD
    A[接收替换请求] --> B{参数验证}
    B -->|失败| C[返回参数错误]
    B -->|成功| D{新旧ReportNo相同?}
    D -->|是| E[返回业务错误]
    D -->|否| F[查询原始数据]
    F --> G{原数据存在?}
    G -->|否| H[返回数据不存在错误]
    G -->|是| I[开启事务]
    I --> J[设置原数据无效]
    J --> K{无效化成功?}
    K -->|否| L[事务回滚]
    K -->|是| M[复制并创建新数据]
    M --> N[插入新数据]
    N --> O{插入成功?}
    O -->|否| P[事务回滚]
    O -->|是| Q[提交事务]
    Q --> R[返回成功结果]
```

### 4.3 数据无效化流程

```mermaid
flowchart TD
    A[接收无效化请求] --> B{参数验证}
    B -->|失败| C[返回参数错误]
    B -->|成功| D[构建更新条件]
    D --> E[执行数据库更新]
    E --> F{更新结果}
    F -->|影响行数=0| G[返回数据不存在错误]
    F -->|影响行数>0| H[记录操作日志]
    H --> I[返回成功结果]
```

## 5. 核心改造点分析

### 5.1 Controller层改造

**文件**: `testdatabiz-web/src/main/java/com/sgs/testdatabiz/web/controllers/SubcontractController.java`

**改造内容**:
- 新增3个REST API接口方法
- 添加Swagger API文档注解
- 统一异常处理和响应格式

**关键代码改动**:
```java
@PostMapping("/querySubcontractData")
@ApiOperation(value = "查询分包数据")
public BaseResponse<List<ReportTestDataInfo>> querySubcontractData(@RequestBody SubcontractQueryReq req) {
    return subContractFacade.querySubcontractData(req);
}

@PostMapping("/replaceSubcontractReportNo")
@ApiOperation(value = "替换分包数据ReportNo")
public BaseResponse<Void> replaceSubcontractReportNo(@RequestBody SubcontractReplaceReq req) {
    return subContractFacade.replaceSubcontractReportNo(req);
}

@PostMapping("/invalidateSubcontractData")
@ApiOperation(value = "设置分包数据为无效状态")
public BaseResponse<Void> invalidateSubcontractData(@RequestBody SubcontractInvalidateReq req) {
    return subContractFacade.invalidateSubcontractData(req);
}
```

### 5.2 Facade层改造

**文件**: 
- `testdatabiz-facade/src/main/java/com/sgs/testdatabiz/facade/SubContractFacade.java`
- `testdatabiz-facade-impl/src/main/java/com/sgs/testdatabiz/facade/impl/SubContractFacadeImpl.java`

**改造内容**:
- 接口定义新增3个方法签名
- 实现类新增3个方法实现，调用Service层

### 5.3 Service层改造

**文件**: `testdatabiz-domain/src/main/java/com/sgs/testdatabiz/domain/service/EnterSubContractService.java`

**改造内容**:
- 新增分包数据查询业务逻辑
- 新增ReportNo替换业务逻辑（事务管理）
- 新增数据无效化业务逻辑
- 添加数据转换和验证方法

### 5.4 数据访问层改造

**文件**: 
- `testdatabiz-dbstorages/src/main/java/com/sgs/testdatabiz/dbstorages/mybatis/extmapper/testdata/TestDataObjectRelExtMapper.java`
- `testdatabiz-dbstorages/src/main/resources/sqlmap/userdefined/TestDataObjectRelExtMapper.xml`

**改造内容**:
- 新增查询有效分包数据的Mapper方法
- 新增批量无效化数据的Mapper方法
- 新增对应的SQL语句实现

### 5.5 Model层改造

**新增文件**:
- `testdatabiz-facade-model/src/main/java/com/sgs/testdatabiz/facade/model/req/subcontract/SubcontractQueryReq.java`
- `testdatabiz-facade-model/src/main/java/com/sgs/testdatabiz/facade/model/req/subcontract/SubcontractReplaceReq.java`
- `testdatabiz-facade-model/src/main/java/com/sgs/testdatabiz/facade/model/req/subcontract/SubcontractInvalidateReq.java`

## 6. 数据库设计

### 6.1 核心数据表

#### `tb_test_data_object_rel` 表结构
```sql
CREATE TABLE `tb_test_data_object_rel` (
  `Id` varchar(36) NOT NULL COMMENT '主键Id',
  `ProductLineCode` varchar(10) NOT NULL COMMENT '订单的BU',
  `LabCode` varchar(50) NOT NULL COMMENT '订单所在的Lab',
  `OrderNo` varchar(50) NOT NULL COMMENT '订单号',
  `ParentOrderNo` varchar(50) DEFAULT NULL COMMENT '发包方订单号',
  `ReportNo` varchar(50) DEFAULT NULL COMMENT '报告号',
  `ObjectNo` varchar(50) DEFAULT NULL COMMENT '对象编号',
  `ExternalId` varchar(50) DEFAULT NULL COMMENT '外部系统主键Id',
  `ExternalNo` varchar(50) DEFAULT NULL COMMENT '外部编号',
  `ExternalObjectNo` varchar(50) DEFAULT NULL COMMENT '外部对象编号',
  `ExcludeCustomerInterface` varchar(10) DEFAULT NULL COMMENT '标记是否回传客户接口',
  `SourceType` int(10) NOT NULL COMMENT '数据源类型',
  `LanguageId` int(10) NOT NULL DEFAULT '0' COMMENT '语言ID',
  `CompleteDate` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `BizVersionId` char(32) NOT NULL COMMENT '业务版本ID',
  `ActiveIndicator` int(10) NOT NULL COMMENT '0: inactive, 1: active',
  `CreatedBy` varchar(50) DEFAULT NULL COMMENT '创建人',
  `CreatedDate` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `ModifiedBy` varchar(50) DEFAULT NULL COMMENT '修改人',
  `ModifiedDate` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `LastModifiedTimestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `LabId` bigint(20) DEFAULT NULL COMMENT '实验室ID',
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 6.2 新增SQL操作

#### 查询有效分包数据
```sql
SELECT Id, ProductLineCode, LabCode, OrderNo, ParentOrderNo, ReportNo, ObjectNo, 
       ExternalId, ExternalNo, ExternalObjectNo, SourceType, LanguageId, 
       CompleteDate, BizVersionId, ActiveIndicator, CreatedBy, CreatedDate, 
       ModifiedBy, ModifiedDate, LabId, ExcludeCustomerInterface
FROM tb_test_data_object_rel
WHERE OrderNo = ? AND ObjectNo = ? AND LabCode = ? AND ReportNo = ? AND ActiveIndicator = 1
ORDER BY CreatedDate DESC;
```

#### 设置分包数据无效
```sql
UPDATE tb_test_data_object_rel
SET ActiveIndicator = 0, ModifiedBy = ?, ModifiedDate = ?
WHERE OrderNo = ? AND ObjectNo = ? AND LabCode = ? AND ReportNo = ? AND ActiveIndicator = 1;
```

### 6.3 建议新增索引

为了提升查询性能，建议添加以下复合索引：

```sql
-- 查询优化索引
CREATE INDEX idx_subcontract_query 
ON tb_test_data_object_rel (OrderNo, ObjectNo, LabCode, ReportNo, ActiveIndicator);

-- 时间排序优化索引  
CREATE INDEX idx_subcontract_created_date
ON tb_test_data_object_rel (CreatedDate DESC);
```

## 7. 影响范围评估

### 7.1 功能影响范围

| 影响模块 | 影响级别 | 影响描述 | 风险评估 |
|----------|----------|----------|---------|
| 分包数据管理 | 高 | 新增3个API接口，扩展现有功能 | 低 - 纯新增功能 |
| 数据库访问层 | 中 | 新增Mapper方法和SQL语句 | 低 - 不影响现有SQL |
| 业务服务层 | 中 | 扩展EnterSubContractService业务逻辑 | 低 - 独立新增方法 |
| API网关 | 低 | 可能需要更新路由配置 | 低 - 标准API扩展 |

### 7.2 数据影响范围

| 数据表 | 操作类型 | 影响描述 | 风险评估 |
|--------|----------|----------|---------|
| tb_test_data_object_rel | SELECT | 根据业务条件查询数据 | 无风险 |
| tb_test_data_object_rel | UPDATE | 修改ActiveIndicator字段为0 | 低风险 - 软删除 |
| tb_test_data_object_rel | INSERT | 插入新的分包数据记录 | 低风险 - 标准插入 |

### 7.3 系统性能影响

- **查询性能**: 通过添加复合索引优化查询性能
- **写入性能**: ReportNo替换涉及事务操作，需要注意锁等待
- **存储空间**: 替换操作会产生新记录，历史数据保留

## 8. 测试回归建议

### 8.1 单元测试覆盖

#### Controller层测试
```java
@Test
public void testQuerySubcontractData() {
    // 测试正常查询场景
    // 测试参数验证场景
    // 测试异常处理场景
}

@Test  
public void testReplaceSubcontractReportNo() {
    // 测试正常替换场景
    // 测试相同ReportNo场景
    // 测试原数据不存在场景
    // 测试事务回滚场景
}

@Test
public void testInvalidateSubcontractData() {
    // 测试正常无效化场景
    // 测试数据不存在场景
    // 测试已无效数据场景
}
```

#### Service层测试
```java
@Test
public void testQuerySubcontractDataService() {
    // 测试业务逻辑正确性
    // 测试数据转换正确性
    // 测试异常处理机制
}

@Test
public void testReplaceReportNoWithTransaction() {
    // 测试事务完整性
    // 测试数据一致性
    // 测试并发场景
}
```

#### DAO层测试
```java
@Test
public void testQueryValidSubcontractData() {
    // 测试SQL查询正确性
    // 测试索引效果
    // 测试分页查询
}

@Test
public void testUpdateSubcontractDataToInvalid() {
    // 测试更新操作正确性
    // 测试影响行数统计
    // 测试条件匹配准确性
}
```

### 8.2 集成测试回归点

#### 现有功能回归
1. **分包数据导入功能 (saveSubCompleteTestData)**
   - 验证导入流程不受影响
   - 验证数据完整性保持
   - 验证状态管理正常

2. **分包数据查询功能 (querySubTestData, getSubTestDataInfo)**
   - 验证现有查询接口正常
   - 验证数据返回格式一致
   - 验证性能无退化

3. **分包数据取消功能 (cancelSubTestData)**
   - 验证取消逻辑不受影响
   - 验证状态变更正常
   - 验证关联数据处理

#### 新功能集成测试
1. **端到端业务流程测试**
   - 导入 → 查询 → 替换 → 查询 → 无效化 → 查询
   - 验证完整业务链路正常

2. **数据一致性测试**
   - 验证ReportNo替换前后数据关联正确
   - 验证ActiveIndicator状态管理准确
   - 验证历史数据可追溯

3. **异常场景测试**
   - 并发操作场景测试
   - 大数据量场景测试
   - 网络异常恢复测试

### 8.3 性能测试建议

#### 查询性能测试
- **数据量**: 10万、50万、100万记录
- **并发数**: 10、50、100、200并发
- **响应时间**: < 100ms (95分位)
- **吞吐量**: > 1000 QPS

#### 替换操作性能测试  
- **事务时间**: < 200ms
- **锁等待时间**: < 50ms
- **并发替换**: 支持10并发操作

#### 系统资源监控
- **CPU使用率**: < 70%
- **内存使用率**: < 80% 
- **数据库连接池**: < 80%占用率
- **磁盘I/O**: 监控读写频率

## 9. 实施计划

### 9.1 开发阶段

| 阶段 | 任务内容 | 预估工期 | 负责人 | 交付物 |
|------|----------|----------|--------|---------|
| 阶段1 | 请求响应模型类设计与实现 | 1天 | 后端开发 | Model类代码 |
| 阶段2 | 数据访问层扩展实现 | 1天 | 后端开发 | Mapper接口和SQL |
| 阶段3 | 业务服务层逻辑实现 | 2天 | 后端开发 | Service业务代码 |
| 阶段4 | Facade层和Controller层实现 | 1天 | 后端开发 | API接口代码 |
| 阶段5 | 单元测试编写 | 2天 | 后端开发 | 测试用例代码 |

### 9.2 测试阶段

| 阶段 | 任务内容 | 预估工期 | 负责人 | 交付物 |
|------|----------|----------|--------|---------|
| 阶段1 | 单元测试执行 | 1天 | 测试工程师 | 测试报告 |
| 阶段2 | 集成测试执行 | 2天 | 测试工程师 | 集成测试报告 |
| 阶段3 | 性能测试执行 | 1天 | 测试工程师 | 性能测试报告 |
| 阶段4 | 回归测试执行 | 1天 | 测试工程师 | 回归测试报告 |

### 总工期预估: **12个工作日**

## 10. 风险评估与应对策略

### 10.1 技术风险

| 风险项 | 风险级别 | 影响描述 | 应对策略 |
|--------|----------|----------|---------|
| 数据库事务死锁 | 中 | ReportNo替换时可能出现锁竞争 | 优化SQL执行顺序，添加超时机制 |
| 大数据量查询性能 | 中 | 分包数据量大时查询响应慢 | 添加复合索引，实现分页查询 |
| 并发数据一致性 | 高 | 同时替换相同数据可能冲突 | 使用乐观锁或分布式锁 |

### 10.2 业务风险

| 风险项 | 风险级别 | 影响描述 | 应对策略 |
|--------|----------|----------|---------|
| 数据误操作 | 高 | 错误的无效化或替换操作 | 添加操作确认机制，记录操作日志 |
| 历史数据丢失 | 中 | 替换操作可能影响数据追溯 | 保留原数据，只修改状态字段 |
| 接口调用频率过高 | 中 | 大量查询请求影响系统性能 | 实现接口限流，添加缓存机制 |

### 10.3 运维风险

| 风险项 | 风险级别 | 影响描述 | 应对策略 |
|--------|----------|----------|---------|
| 数据库存储空间 | 低 | 替换操作增加存储消耗 | 监控存储使用率，定期清理 |
| 系统资源消耗 | 低 | 新功能增加CPU和内存使用 | 性能监控，弹性扩容 |

## 11. 错误码定义

| 错误码 | 错误描述 | 场景说明 |
|--------|----------|---------|
| 10001 | 参数验证失败 | 必填参数为空或格式不正确 |
| 10002 | 数据不存在 | 查询或操作的分包数据不存在 |
| 10003 | 数据已无效 | 操作的数据已经被设置为无效状态 |
| 10004 | ReportNo重复 | 新ReportNo与原ReportNo相同 |
| 10005 | 事务处理失败 | 数据库事务执行失败 |
| 10006 | 系统繁忙 | 系统负载过高，请稍后重试 |

## 12. 附录

### 12.1 相关文档

- [testdatabiz项目架构说明](./doc/技术架构说明.md)
- [分包数据模型设计](./doc/数据模型说明.md)
- [SubContractFacade接口文档](./doc/facade/SubContractFacade.md)

### 12.2 关键配置

#### 数据库连接配置
```properties
# MySQL配置
spring.datasource.url=***************************************
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# 连接池配置
spring.datasource.druid.initial-size=5
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-active=20
```

#### MyBatis配置
```properties
# MyBatis配置
mybatis.mapper-locations=classpath:sqlmap/**/*.xml
mybatis.type-aliases-package=com.sgs.testdatabiz.dbstorages.mybatis.model
```

## 13. 总结

本设计文档详细阐述了分包数据管理API接口的扩展方案，包括三个新增API接口的设计、实现和测试方案。方案遵循Testdatabiz项目的DDD架构设计原则，保证了代码的一致性和可维护性。

### 13.1 主要交付物
1. **API接口设计** - 3个新的REST API接口
2. **数据库设计** - 新增SQL语句和索引优化
3. **业务流程** - 完整的业务处理流程设计
4. **测试策略** - 全面的测试计划和回归建议
5. **风险控制** - 技术和业务风险的识别和应对

### 13.2 核心价值
- **功能扩展** - 在不影响现有功能的基础上，新增了分包数据的管理能力
- **数据安全** - 采用软删除机制，保证数据的可追溯性
- **性能优化** - 通过索引优化和事务管理保证系统性能
- **扩展性** - 遵循现有架构模式，便于后续维护和扩展

### 13.3 实施效果

通过本方案的实施，将显著提升SGS Report Data系统在分包数据管理方面的能力，为业务系统提供更加完善和灵活的数据管理服务。新增的三个API接口将有效解决当前分包数据查询、替换和生命周期管理的需求，同时保证了系统的稳定性和可维护性。