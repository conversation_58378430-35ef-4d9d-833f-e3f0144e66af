package com.sgs.testdatabiz.web.controllers;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.testdatabiz.facade.SubContractFacade;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataDTO;
import com.sgs.testdatabiz.facade.model.req.backsubcontract.SubCompleteTestDataReq;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.SubcontractNoReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractQueryReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractReplaceReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractInvalidateReq;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.facade.v2.ReportDataBizService;
import com.sgs.testdatabiz.facade.v2.ReportTestDataService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/subContract")
@Api(value = "/subContract", tags = "subContract")
public class SubcontractController {

    private static final Logger logger = LoggerFactory.getLogger(SubcontractController.class);

    @Autowired
    private SubContractFacade subContractFacade;

    @Autowired
    private ReportDataBizService reportDataBizService;

    @Autowired
    private ReportTestDataService reportTestDataService;

    @PostMapping("/saveSubCompleteTestData")
    @ApiOperation(value = "分包回传 保存 testData")
    public BaseResponse saveSubCompleteTestData(@RequestBody SubCompleteTestDataReq reqObject) {
        return subContractFacade.saveSubCompleteTestData(reqObject);
    }

    @PostMapping("/importSubcontractData")
    @ApiOperation(value = "分包回传 批量导入 testData")
    public  BaseResponse<Void> importSubcontractData(@RequestBody ReportTestDataInfo reqObject) {
        return reportTestDataService.importData(reqObject);
    }

    @PostMapping("/getSubTestDataInfo")
    @ApiOperation(value = "分包数据补充")
    public BaseResponse<?> getSubTestDataInfo(@RequestBody ReportDataDTO req) {
        return reportDataBizService.getSubTestDataInfo(req);
    }

    /**
     * 查询分包数据
     * 
     * 根据订单号、对象编号、实验室代码、报告号查询有效的分包数据
     */
    @PostMapping("/querySubcontractData")
    @ApiOperation(value = "查询分包数据", notes = "根据订单号、对象编号、实验室代码、报告号查询有效的分包数据")
    public BaseResponse<List<ReportTestDataInfo>> querySubcontractData(@RequestBody SubcontractQueryReq req) {
        logger.info("接收到查询分包数据请求: {}", req);
        return subContractFacade.querySubcontractData(req);
    }

    /**
     * 替换分包数据ReportNo
     * 
     * 通过原数据无效+新增数据的方式实现ReportNo替换
     */
    @PostMapping("/replaceSubcontractReportNo")
    @ApiOperation(value = "替换分包数据ReportNo", notes = "通过原数据无效+新增数据的方式实现ReportNo替换")
    public BaseResponse<Void> replaceSubcontractReportNo(@RequestBody SubcontractReplaceReq req) {
        logger.info("接收到替换分包数据ReportNo请求: {}", req);
        return subContractFacade.replaceSubcontractReportNo(req);
    }

    /**
     * 设置分包数据为无效状态
     * 
     * 通过更新ActiveIndicator字段为0实现软删除
     */
    @PostMapping("/invalidateSubcontractData")
    @ApiOperation(value = "设置分包数据为无效状态", notes = "通过更新ActiveIndicator字段为0实现软删除")
    public BaseResponse<Void> invalidateSubcontractData(@RequestBody SubcontractInvalidateReq req) {
        logger.info("接收到无效化分包数据请求: {}", req);
        return subContractFacade.invalidateSubcontractData(req);
    }


}
