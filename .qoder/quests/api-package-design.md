# 分包数据管理API接口扩展设计文档

## 文档信息

| 项目名称 | testdatabiz.iapi.sgs.net |
|---------|-------------------------|
| 功能模块 | 分包数据管理API接口扩展 |
| 文档版本 | v1.0 |
| 创建日期 | 2025-01-15 |
| 文档类型 | API包设计文档 |

## 1. 项目概述

### 1.1 需求背景

Report Data (RD) 系统作为SGS的业务数据存储服务，当前已具备分包数据导入能力。为完善分包数据管理功能，需要在现有 `SubcontractController` 基础上扩展三个新的API接口：

1. **分包数据查询接口** - 根据业务条件查询有效的分包数据
2. **分包数据ReportNo替换接口** - 通过原数据无效+新增数据的方式实现ReportNo替换  
3. **分包数据无效化接口** - 将指定分包数据设置为无效状态

### 1.2 技术架构

项目采用DDD领域驱动设计，技术栈包括：
- **后端框架**: Spring Boot 2.4.2, Java 8+
- **服务框架**: Dubbo 2.8.4  
- **数据库**: MySQL 5.1.31, MyBatis ORM
- **构建工具**: Maven 3.x

## 2. 系统架构设计

### 2.1 整体架构

```mermaid
graph TB
    subgraph "Web Layer"
        SC[SubcontractController]
    end
    
    subgraph "Facade Layer" 
        SCF[SubContractFacade]
        SCFI[SubContractFacadeImpl]
    end
    
    subgraph "Domain Layer"
        ESCS[EnterSubContractService]
    end
    
    subgraph "Data Access Layer"
        TOREM[TestDataObjectRelExtMapper]
    end
    
    subgraph "Database"
        TDOR[(tb_test_data_object_rel)]
    end
    
    SC --> SCF
    SCF --> SCFI
    SCFI --> ESCS
    ESCS --> TOREM
    TOREM --> TDOR
```

### 2.2 数据流转时序

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as SubcontractController
    participant Facade as SubContractFacade
    participant Service as EnterSubContractService
    participant Mapper as TestDataObjectRelExtMapper
    participant DB as MySQL数据库
    
    Client->>Controller: HTTP请求
    Controller->>Facade: 调用Facade方法
    Facade->>Service: 调用业务逻辑
    Service->>Mapper: 数据库操作
    Mapper->>DB: SQL执行
    DB-->>Mapper: 返回结果
    Mapper-->>Service: 返回数据
    Service-->>Facade: 返回业务结果
    Facade-->>Controller: 返回响应
    Controller-->>Client: HTTP响应
```

## 3. API接口设计

### 3.1 分包数据查询接口

#### 基本信息
- **接口路径**: `POST /subContract/querySubcontractData`
- **接口描述**: 根据订单号、对象编号、实验室代码、报告号查询有效的分包数据
- **接口类型**: 查询接口

#### 请求参数

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| orderNo | String | 是 | 订单号 | ORD-2024-001 |
| objectNo | String | 是 | 对象编号/分包号 | SC-2024-001 |
| labCode | String | 是 | 实验室代码 | GZ |
| reportNo | String | 是 | 报告号 | RPT-2024-001 |

#### 响应参数

```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "orderNo": "ORD-2024-001",
      "parentOrderNo": "PORD-2024-001", 
      "subContractNo": "SC-2024-001",
      "reportNo": "RPT-2024-001",
      "objectNo": "SC-2024-001",
      "labCode": "GZ",
      "productLineCode": "CTS",
      "externalId": "EXT-001",
      "externalNo": "SLIM-001",
      "externalObjectNo": "SLIM-OBJ-001",
      "completedDate": "2024-01-10T10:30:00",
      "sourceType": "5",
      "languageId": 1,
      "testMatrixs": []
    }
  ]
}
```

### 3.2 分包数据ReportNo替换接口

#### 基本信息
- **接口路径**: `POST /subContract/replaceSubcontractReportNo`
- **接口描述**: 将原分包数据设置为无效，并新增一条使用新ReportNo的数据
- **接口类型**: 业务操作接口

#### 请求参数

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| orderNo | String | 是 | 订单号 | ORD-2024-001 |
| objectNo | String | 是 | 对象编号/分包号 | SC-2024-001 |
| labCode | String | 是 | 实验室代码 | GZ |
| reportNo | String | 是 | 原报告号 | RPT-2024-001 |
| newReportNo | String | 是 | 新报告号 | RPT-2024-002 |

#### 响应参数

```json
{
  "code": 200,
  "message": "分包数据ReportNo替换成功",
  "data": null
}
```

### 3.3 分包数据无效化接口

#### 基本信息
- **接口路径**: `POST /subContract/invalidateSubcontractData`
- **接口描述**: 将指定分包数据设置为无效状态
- **接口类型**: 状态管理接口

#### 请求参数

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| orderNo | String | 是 | 订单号 | ORD-2024-001 |
| objectNo | String | 是 | 对象编号/分包号 | SC-2024-001 |
| labCode | String | 是 | 实验室代码 | GZ |
| reportNo | String | 是 | 报告号 | RPT-2024-001 |

#### 响应参数

```json
{
  "code": 200,
  "message": "分包数据无效化成功",
  "data": null
}
```

## 4. 业务流程设计

### 4.1 分包数据查询流程

```mermaid
flowchart TD
    A[接收查询请求] --> B{参数验证}
    B -->|失败| C[返回参数错误]
    B -->|成功| D[构建查询条件]
    D --> E[执行数据库查询]
    E --> F{查询结果}
    F -->|无数据| G[返回空列表]
    F -->|有数据| H[转换为业务对象]
    H --> I[返回查询结果]
```

### 4.2 ReportNo替换流程

```mermaid
flowchart TD
    A[接收替换请求] --> B{参数验证}
    B -->|失败| C[返回参数错误]
    B -->|成功| D{新旧ReportNo相同?}
    D -->|是| E[返回业务错误]
    D -->|否| F[查询原始数据]
    F --> G{原数据存在?}
    G -->|否| H[返回数据不存在错误]
    G -->|是| I[开启事务]
    I --> J[设置原数据无效]
    J --> K{无效化成功?}
    K -->|否| L[事务回滚]
    K -->|是| M[复制并创建新数据]
    M --> N[插入新数据]
    N --> O{插入成功?}
    O -->|否| P[事务回滚]
    O -->|是| Q[提交事务]
    Q --> R[返回成功结果]
```

### 4.3 数据无效化流程

```mermaid
flowchart TD
    A[接收无效化请求] --> B{参数验证}
    B -->|失败| C[返回参数错误]
    B -->|成功| D[构建更新条件]
    D --> E[执行数据库更新]
    E --> F{更新结果}
    F -->|影响行数=0| G[返回数据不存在错误]
    F -->|影响行数>0| H[记录操作日志]
    H --> I[返回成功结果]
```

## 5. 代码实现架构

### 5.1 Controller层扩展

**文件位置**: `testdatabiz-web/src/main/java/com/sgs/testdatabiz/web/controllers/SubcontractController.java`

**新增方法**:
```java
@PostMapping("/querySubcontractData")
@ApiOperation(value = "查询分包数据")
public BaseResponse<List<ReportTestDataInfo>> querySubcontractData(@RequestBody SubcontractQueryReq req) {
    return subContractFacade.querySubcontractData(req);
}

@PostMapping("/replaceSubcontractReportNo")
@ApiOperation(value = "替换分包数据ReportNo")
public BaseResponse<Void> replaceSubcontractReportNo(@RequestBody SubcontractReplaceReq req) {
    return subContractFacade.replaceSubcontractReportNo(req);
}

@PostMapping("/invalidateSubcontractData")
@ApiOperation(value = "设置分包数据为无效状态")
public BaseResponse<Void> invalidateSubcontractData(@RequestBody SubcontractInvalidateReq req) {
    return subContractFacade.invalidateSubcontractData(req);
}
```

### 5.2 Facade层扩展

**接口文件**: `testdatabiz-facade/src/main/java/com/sgs/testdatabiz/facade/SubContractFacade.java`

**新增接口方法**:
```java
/**
 * 查询分包数据
 */
BaseResponse<List<ReportTestDataInfo>> querySubcontractData(SubcontractQueryReq req);

/**
 * 替换分包数据ReportNo
 */
BaseResponse<Void> replaceSubcontractReportNo(SubcontractReplaceReq req);

/**
 * 设置分包数据为无效状态
 */
BaseResponse<Void> invalidateSubcontractData(SubcontractInvalidateReq req);
```

**实现文件**: `testdatabiz-facade-impl/src/main/java/com/sgs/testdatabiz/facade/impl/SubContractFacadeImpl.java`

### 5.3 Service层扩展

**文件位置**: `testdatabiz-domain/src/main/java/com/sgs/testdatabiz/domain/service/EnterSubContractService.java`

**新增业务方法**:
- `querySubcontractData()` - 查询分包数据业务逻辑
- `replaceSubcontractReportNo()` - ReportNo替换业务逻辑（事务管理）
- `invalidateSubcontractData()` - 数据无效化业务逻辑

### 5.4 数据访问层扩展

**Mapper接口**: `testdatabiz-dbstorages/src/main/java/com/sgs/testdatabiz/dbstorages/mybatis/extmapper/testdata/TestDataObjectRelExtMapper.java`

**新增方法**:
```java
/**
 * 查询有效的分包数据
 */
List<ReportTestDataInfo> queryValidSubcontractData(@Param("orderNo") String orderNo,
                                                   @Param("objectNo") String objectNo,
                                                   @Param("labCode") String labCode,
                                                   @Param("reportNo") String reportNo);

/**
 * 批量设置分包数据为无效状态
 */
int invalidateSubcontractData(@Param("orderNo") String orderNo,
                             @Param("objectNo") String objectNo,
                             @Param("labCode") String labCode,
                             @Param("reportNo") String reportNo,
                             @Param("modifiedBy") String modifiedBy,
                             @Param("modifiedDate") Date modifiedDate);
```

### 5.5 请求响应模型

**新增文件**:

1. `testdatabiz-facade-model/src/main/java/com/sgs/testdatabiz/facade/model/req/subcontract/SubcontractQueryReq.java`
2. `testdatabiz-facade-model/src/main/java/com/sgs/testdatabiz/facade/model/req/subcontract/SubcontractReplaceReq.java`
3. `testdatabiz-facade-model/src/main/java/com/sgs/testdatabiz/facade/model/req/subcontract/SubcontractInvalidateReq.java`

## 6. 数据库设计

### 6.1 核心数据表

**表名**: `tb_test_data_object_rel`

**主要字段**:
- `Id` - 主键
- `OrderNo` - 订单号
- `ObjectNo` - 对象编号
- `LabCode` - 实验室代码
- `ReportNo` - 报告号
- `ActiveIndicator` - 有效标识 (1:有效, 0:无效)
- `CreatedBy` / `CreatedDate` - 创建信息
- `ModifiedBy` / `ModifiedDate` - 修改信息

### 6.2 新增SQL操作

#### 查询有效分包数据
```sql
SELECT Id, ProductLineCode, LabCode, OrderNo, ParentOrderNo, ReportNo, ObjectNo,
       ExternalId, ExternalNo, ExternalObjectNo, SourceType, LanguageId,
       CompleteDate, ActiveIndicator, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate
FROM tb_test_data_object_rel
WHERE OrderNo = ? AND ObjectNo = ? AND LabCode = ? AND ReportNo = ? AND ActiveIndicator = 1
ORDER BY CreatedDate DESC;
```

#### 设置分包数据无效
```sql
UPDATE tb_test_data_object_rel
SET ActiveIndicator = 0, ModifiedBy = ?, ModifiedDate = ?
WHERE OrderNo = ? AND ObjectNo = ? AND LabCode = ? AND ReportNo = ? AND ActiveIndicator = 1;
```

### 6.3 索引优化建议

```sql
-- 查询优化索引
CREATE INDEX idx_subcontract_query
ON tb_test_data_object_rel (OrderNo, ObjectNo, LabCode, ReportNo, ActiveIndicator);

-- 时间排序优化索引
CREATE INDEX idx_subcontract_created_date
ON tb_test_data_object_rel (CreatedDate DESC);
```

## 7. 核心改造点分析

### 7.1 Web层改造
- 新增3个REST API接口方法
- 添加Swagger API文档注解
- 统一异常处理和响应格式

### 7.2 Facade层改造
- 接口定义新增3个方法签名
- 实现类新增3个方法实现，调用Service层

### 7.3 Service层改造
- 新增分包数据查询业务逻辑
- 新增ReportNo替换业务逻辑（事务管理）
- 新增数据无效化业务逻辑
- 添加数据转换和验证方法

### 7.4 数据访问层改造
- 新增查询有效分包数据的Mapper方法
- 新增批量无效化数据的Mapper方法
- 新增对应的SQL语句实现

### 7.5 Model层改造
- 新增3个请求模型类
- 复用现有响应模型类

## 8. 影响范围评估

### 8.1 功能影响

| 影响模块 | 影响级别 | 影响描述 | 风险评估 |
|----------|----------|----------|---------|
| 分包数据管理 | 高 | 新增3个API接口 | 低 - 纯新增功能 |
| 数据库访问层 | 中 | 新增Mapper方法和SQL语句 | 低 - 不影响现有SQL |
| 业务服务层 | 中 | 扩展业务逻辑 | 低 - 独立新增方法 |

### 8.2 数据影响

| 数据表 | 操作类型 | 影响描述 | 风险评估 |
|--------|----------|----------|---------|
| tb_test_data_object_rel | SELECT | 根据业务条件查询数据 | 无风险 |
| tb_test_data_object_rel | UPDATE | 修改ActiveIndicator字段为0 | 低风险 - 软删除 |
| tb_test_data_object_rel | INSERT | 插入新的分包数据记录 | 低风险 - 标准插入 |

## 9. 测试策略

### 9.1 单元测试
- Controller层参数验证和异常处理测试
- Service层业务逻辑正确性测试
- DAO层SQL查询和更新操作测试

### 9.2 集成测试
- 现有分包数据功能回归测试
- 新功能端到端业务流程测试
- 数据一致性和并发场景测试

### 9.3 性能测试
- 查询性能测试：支持100万记录，响应时间<100ms
- 替换操作性能测试：事务时间<200ms
- 并发测试：支持200并发操作

## 10. 错误码定义

| 错误码 | 错误描述 | 场景说明 |
|--------|----------|---------|
| 10001 | 参数验证失败 | 必填参数为空或格式不正确 |
| 10002 | 数据不存在 | 查询或操作的分包数据不存在 |
| 10003 | 数据已无效 | 操作的数据已经被设置为无效状态 |
| 10004 | ReportNo重复 | 新ReportNo与原ReportNo相同 |
| 10005 | 事务处理失败 | 数据库事务执行失败 |
| 10006 | 系统繁忙 | 系统负载过高，请稍后重试 |

## 11. 实施计划

### 11.1 开发阶段

| 阶段 | 任务内容 | 预估工期 | 交付物 |
|------|----------|----------|---------|
| 阶段1 | 请求响应模型类设计与实现 | 1天 | Model类代码 |
| 阶段2 | 数据访问层扩展实现 | 1天 | Mapper接口和SQL |
| 阶段3 | 业务服务层逻辑实现 | 2天 | Service业务代码 |
| 阶段4 | Facade层和Controller层实现 | 1天 | API接口代码 |
| 阶段5 | 单元测试编写 | 2天 | 测试用例代码 |

### 11.2 测试阶段

| 阶段 | 任务内容 | 预估工期 | 交付物 |
|------|----------|----------|---------|
| 阶段1 | 单元测试执行 | 1天 | 测试报告 |
| 阶段2 | 集成测试执行 | 2天 | 集成测试报告 |
| 阶段3 | 性能测试执行 | 1天 | 性能测试报告 |
| 阶段4 | 回归测试执行 | 1天 | 回归测试报告 |

**总工期预估**: 12个工作日

## 12. 风险控制

### 12.1 技术风险

| 风险项 | 风险级别 | 应对策略 |
|--------|----------|---------|
| 数据库事务死锁 | 中 | 优化SQL执行顺序，添加超时机制 |
| 大数据量查询性能 | 中 | 添加复合索引，实现分页查询 |
| 并发数据一致性 | 高 | 使用乐观锁或分布式锁 |

### 12.2 业务风险

| 风险项 | 风险级别 | 应对策略 |
|--------|----------|---------|
| 数据误操作 | 高 | 添加操作确认机制，记录操作日志 |
| 历史数据丢失 | 中 | 保留原数据，只修改状态字段 |
| 接口调用频率过高 | 中 | 实现接口限流，添加缓存机制 |